package com.lw.apcc.module.erp.dal.mapper.purchase;


import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.lw.apcc.common.pojo.PageResult;
import com.lw.apcc.common.util.collection.CollectionUtils;
import com.lw.apcc.common.constants.IdConstants;
import com.lw.apcc.framework.mybatis.core.mapper.BaseMapperX;
import com.lw.apcc.framework.mybatis.core.query.MPJLambdaWrapperX;
import com.lw.apcc.module.erp.controller.admin.dailystatistic.vo.ReceiptPaymentDailyStatisticReqVO;
import com.lw.apcc.module.erp.controller.admin.purchase.vo.in.ErpPurchaseInPageReqVO;
import com.lw.apcc.module.erp.dal.dataobject.purchase.ErpPurchaseInDO;
import com.lw.apcc.module.erp.dal.dataobject.purchase.ErpPurchaseInItemDO;
import com.lw.apcc.module.erp.enums.ErpAuditStatus;
import com.lw.apcc.module.erp.utils.UnitUtil;
import org.apache.ibatis.annotations.Mapper;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Collection;
import java.util.List;
import java.util.Objects;

/**
 * ERP 采购入库 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface ErpPurchaseInMapper extends BaseMapperX<ErpPurchaseInDO> {

    /**
     * 获取单个采购入库
     *
     * @param id 编号
     * @return 采购入库
     */
    default ErpPurchaseInDO selectById(Long id) {
        // 是当前用户所属的经营主体的采购订单
        return selectOne(new MPJLambdaWrapperX<ErpPurchaseInDO>()
                .eq(ErpPurchaseInDO::getId, id)
                .in(ErpPurchaseInDO::getUnitId, UnitUtil.getUnitIds()));
    }

    @Override
    default List<ErpPurchaseInDO> selectBatchIds(Collection<? extends Serializable> ids) {
        return selectList(new MPJLambdaWrapperX<ErpPurchaseInDO>()
                .in(ErpPurchaseInDO::getId, ids)
                .in(ErpPurchaseInDO::getUnitId, UnitUtil.getUnitIds()));
    }

    /**
     * 分页查询采购入库
     *
     * @param reqVO 查询条件
     * @return 采购入库列表
     */
    default PageResult<ErpPurchaseInDO> selectPage(ErpPurchaseInPageReqVO reqVO) {

        List<Long> unitIds = UnitUtil.getUnitIds();
        // 是当前用户所属的经营主体的数据
        if (unitIds.isEmpty() || unitIds.contains(IdConstants.NON_EXISTENT_ID)) {
            return PageResult.empty();
        }

        MPJLambdaWrapperX<ErpPurchaseInDO> query = new MPJLambdaWrapperX<ErpPurchaseInDO>()
                .likeIfPresent(ErpPurchaseInDO::getNo, reqVO.getNo())
                .eqIfPresent(ErpPurchaseInDO::getSupplierId, reqVO.getSupplierId())
                .betweenIfPresent(ErpPurchaseInDO::getInTime, reqVO.getInTime())
                .eqIfPresent(ErpPurchaseInDO::getStatus, reqVO.getStatus())
                .likeIfPresent(ErpPurchaseInDO::getRemark, reqVO.getRemark())
                .eqIfPresent(ErpPurchaseInDO::getCreatorId, reqVO.getCreator())
                .eqIfPresent(ErpPurchaseInDO::getAccountId, reqVO.getAccountId())
                .likeIfPresent(ErpPurchaseInDO::getOrderNo, reqVO.getOrderNo())
                .inIfPresent(ErpPurchaseInDO::getUnitId, unitIds)
                .eqIfPresent(ErpPurchaseInDO::getUnitId, reqVO.getUnitId())
                .orderByDesc(ErpPurchaseInDO::getId);


        // 付款状态。为什么需要 t. 的原因，是因为联表查询时，需要指定表名，不然会报字段不存在的错误
        if (Objects.equals(reqVO.getPaymentStatus(), ErpPurchaseInPageReqVO.PAYMENT_STATUS_NONE)) {
            query.eq(ErpPurchaseInDO::getPaymentPrice, 0);
        } else if (Objects.equals(reqVO.getPaymentStatus(), ErpPurchaseInPageReqVO.PAYMENT_STATUS_PART)) {
            query.gt(ErpPurchaseInDO::getPaymentPrice, 0).apply("t.payment_price < t.total_price");
        } else if (Objects.equals(reqVO.getPaymentStatus(), ErpPurchaseInPageReqVO.PAYMENT_STATUS_ALL)) {
            query.apply("t.payment_price = t.total_price");
        }
        if (Boolean.TRUE.equals(reqVO.getPaymentEnable())) {
            query.eq(ErpPurchaseInDO::getStatus, ErpAuditStatus.APPROVE.getStatus())
                    .apply("t.payment_price < t.total_price");
        }
        if (reqVO.getWarehouseId() != null || reqVO.getProductId() != null) {
            query.leftJoin(ErpPurchaseInItemDO.class, ErpPurchaseInItemDO::getInId, ErpPurchaseInDO::getId)
                    .eq(reqVO.getWarehouseId() != null, ErpPurchaseInItemDO::getWarehouseId, reqVO.getWarehouseId())
                    .eq(reqVO.getProductId() != null, ErpPurchaseInItemDO::getProductId, reqVO.getProductId())
                    .groupBy(ErpPurchaseInDO::getId); // 避免 1 对多查询，产生相同的 1
        }
        return selectJoinPage(reqVO, ErpPurchaseInDO.class, query);
    }

    default int updateByIdAndStatus(Long id, Integer status, ErpPurchaseInDO updateObj) {
        return update(updateObj, new LambdaUpdateWrapper<ErpPurchaseInDO>()
                .eq(ErpPurchaseInDO::getId, id)
                .eq(ErpPurchaseInDO::getStatus, status)
                .in(ErpPurchaseInDO::getUnitId, UnitUtil.getUnitIds())
        );
    }

    default ErpPurchaseInDO selectByNo(String no) {
        // 是当前用户所属的经营主体的采购订单
        return selectOne(new MPJLambdaWrapperX<ErpPurchaseInDO>()
                .eq(ErpPurchaseInDO::getNo, no)
                .in(ErpPurchaseInDO::getUnitId, UnitUtil.getUnitIds()));
    }

    /**
     * 查询采购订单对应的采购入库单列表
     *
     * @param orderId 采购订单编号
     * @return 采购入库单列表
     */
    default List<ErpPurchaseInDO> selectListByOrderId(Long orderId) {
        // 是当前用户所属的经营主体的采购订单
        return selectList(new MPJLambdaWrapperX<ErpPurchaseInDO>()
                .eq(ErpPurchaseInDO::getOrderId, orderId)
                .in(ErpPurchaseInDO::getUnitId, UnitUtil.getUnitIds()));
    }

    default PageResult<ErpPurchaseInDO> pagePurchaseInByDate(ReceiptPaymentDailyStatisticReqVO reqVO) {

        MPJLambdaWrapperX<ErpPurchaseInDO> query = new MPJLambdaWrapperX<ErpPurchaseInDO>()

                .betweenIfPresent(ErpPurchaseInDO::getInTime, reqVO.getStartDate(), reqVO.getEndDate())
                .eqIfPresent(ErpPurchaseInDO::getStatus, ErpAuditStatus.APPROVE.getStatus())
                .eqIfPresent(ErpPurchaseInDO::getUnitId, reqVO.getUnitId())
                .orderByDesc(ErpPurchaseInDO::getCreateTime);

        if (reqVO.getWarehouseId() != null) {
            query.leftJoin(ErpPurchaseInItemDO.class, ErpPurchaseInItemDO::getInId, ErpPurchaseInDO::getId)
                    .eq(reqVO.getWarehouseId() != null, ErpPurchaseInItemDO::getWarehouseId, reqVO.getWarehouseId())
                    .groupBy(ErpPurchaseInDO::getId); // 避免 1 对多查询，产生相同的 1
        }
        return selectJoinPage(reqVO, ErpPurchaseInDO.class, query);
    }

    /**
     * 获取所有入库量
     *
     * @return
     */
    default BigDecimal getStockInTotal(LocalDateTime date) {
        List<ErpPurchaseInDO> list = selectList(new MPJLambdaWrapperX<ErpPurchaseInDO>()
                .eq(ErpPurchaseInDO::getInTime, date)
                .eq(ErpPurchaseInDO::getStatus, ErpAuditStatus.APPROVE.getStatus())
        );

        return CollectionUtils.bigDecimalToSum(list,ErpPurchaseInDO::getTotalCount);
    }

    /**
     * 获取最近几天的入库单
     *
     * @param days
     * @return
     */
    default List<ErpPurchaseInDO> selectListByRecentlyDays(int days) {
        List<ErpPurchaseInDO> list = selectList(
                Wrappers.<ErpPurchaseInDO>lambdaQuery()
                        .ge(ErpPurchaseInDO::getInTime, LocalDate.now().minusDays(days - 1))
                        .eq(ErpPurchaseInDO::getStatus, ErpAuditStatus.APPROVE.getStatus())
        );
        return list;
    }

}
