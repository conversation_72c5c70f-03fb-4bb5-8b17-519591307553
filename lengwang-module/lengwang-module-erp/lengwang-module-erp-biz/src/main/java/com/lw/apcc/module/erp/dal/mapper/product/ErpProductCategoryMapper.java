package com.lw.apcc.module.erp.dal.mapper.product;

import com.lw.apcc.common.constants.IdConstants;
import com.lw.apcc.framework.mybatis.core.mapper.BaseMapperX;
import com.lw.apcc.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.lw.apcc.framework.mybatis.core.query.MPJLambdaWrapperX;
import com.lw.apcc.module.erp.controller.admin.product.vo.category.ErpProductCategoryListReqVO;
import com.lw.apcc.module.erp.dal.dataobject.product.ErpProductCategoryDO;
import com.lw.apcc.module.erp.utils.UnitUtil;
import org.apache.ibatis.annotations.Mapper;

import java.util.ArrayList;
import java.util.List;

/**
 * ERP 产品分类 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface ErpProductCategoryMapper extends BaseMapperX<ErpProductCategoryDO> {

    default ErpProductCategoryDO selectById(Long id) {
        // 是当前用户所属的经营主体的数据
        return selectOne(new MPJLambdaWrapperX<ErpProductCategoryDO>()
                .eq(ErpProductCategoryDO::getId, id)
                .in(ErpProductCategoryDO::getUnitId, UnitUtil.getUnitIds()));
    }

    default List<ErpProductCategoryDO> selectList(ErpProductCategoryListReqVO reqVO) {

        List<Long> unitIds = UnitUtil.getUnitIds();
        // 是当前用户所属的经营主体的数据
        if (unitIds.isEmpty() || unitIds.contains(IdConstants.NON_EXISTENT_ID)) {
            return new ArrayList<>();
        }

        return selectList(new LambdaQueryWrapperX<ErpProductCategoryDO>()
                .likeIfPresent(ErpProductCategoryDO::getName, reqVO.getName())
                .eqIfPresent(ErpProductCategoryDO::getStatus, reqVO.getStatus())
                .inIfPresent(ErpProductCategoryDO::getUnitId, unitIds)
                .eqIfPresent(ErpProductCategoryDO::getUnitId, reqVO.getUnitId())
                .orderByDesc(ErpProductCategoryDO::getId));
    }

    default ErpProductCategoryDO selectByParentIdAndName(Long parentId, String name) {
        return selectOne(new MPJLambdaWrapperX<ErpProductCategoryDO>()
                .eq(ErpProductCategoryDO::getParentId, parentId)
                .eq(ErpProductCategoryDO::getName, name)
                .in(ErpProductCategoryDO::getUnitId, UnitUtil.getUnitIds()));
    }

    default ErpProductCategoryDO selectByCodeAndUnitId(String code, Long unitId) {
        return selectOne(new MPJLambdaWrapperX<ErpProductCategoryDO>()
                .eq(ErpProductCategoryDO::getCode, code)
                .eq(ErpProductCategoryDO::getUnitId, unitId));
    }

    default Long selectCountByParentId(Long parentId) {
        return selectCount(new MPJLambdaWrapperX<ErpProductCategoryDO>()
                .eq(ErpProductCategoryDO::getParentId, parentId)
                .in(ErpProductCategoryDO::getUnitId, UnitUtil.getUnitIds()));
    }

}
