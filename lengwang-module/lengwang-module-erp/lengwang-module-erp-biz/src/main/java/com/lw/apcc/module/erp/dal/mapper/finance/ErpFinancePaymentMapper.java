package com.lw.apcc.module.erp.dal.mapper.finance;

import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.lw.apcc.common.pojo.PageResult;
import com.lw.apcc.common.constants.IdConstants;
import com.lw.apcc.framework.mybatis.core.mapper.BaseMapperX;
import com.lw.apcc.framework.mybatis.core.query.MPJLambdaWrapperX;
import com.lw.apcc.module.erp.controller.admin.finance.vo.payment.ErpFinancePaymentPageReqVO;
import com.lw.apcc.module.erp.dal.dataobject.finance.ErpFinancePaymentDO;
import com.lw.apcc.module.erp.dal.dataobject.finance.ErpFinancePaymentItemDO;
import com.lw.apcc.module.erp.utils.UnitUtil;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * ERP 付款单 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface ErpFinancePaymentMapper extends BaseMapperX<ErpFinancePaymentDO> {

    /**
     * 获取单个付款单
     *
     * @param id 编号
     * @return 付款单
     */
    default ErpFinancePaymentDO selectById(Long id) {
        // 是当前用户所属的经营主体的数据
        return selectOne(new MPJLambdaWrapperX<ErpFinancePaymentDO>()
                .eq(ErpFinancePaymentDO::getId, id)
                .in(ErpFinancePaymentDO::getUnitId, UnitUtil.getUnitIds()));
    }

    default PageResult<ErpFinancePaymentDO> selectPage(ErpFinancePaymentPageReqVO reqVO) {

        List<Long> unitIds = UnitUtil.getUnitIds();
        // 是当前用户所属的经营主体的数据
        if (unitIds.isEmpty() || unitIds.contains(IdConstants.NON_EXISTENT_ID)) {
            return PageResult.empty();
        }

        MPJLambdaWrapperX<ErpFinancePaymentDO> query = new MPJLambdaWrapperX<ErpFinancePaymentDO>()
                .likeIfPresent(ErpFinancePaymentDO::getNo, reqVO.getNo())
                .betweenIfPresent(ErpFinancePaymentDO::getPaymentTime, reqVO.getPaymentTime())
                .eqIfPresent(ErpFinancePaymentDO::getSupplierId, reqVO.getSupplierId())
                .eqIfPresent(ErpFinancePaymentDO::getCreatorId, reqVO.getCreator())
                .eqIfPresent(ErpFinancePaymentDO::getFinanceUserId, reqVO.getFinanceUserId())
                .eqIfPresent(ErpFinancePaymentDO::getAccountId, reqVO.getAccountId())
                .eqIfPresent(ErpFinancePaymentDO::getStatus, reqVO.getStatus())
                .likeIfPresent(ErpFinancePaymentDO::getRemark, reqVO.getRemark())
                .inIfPresent(ErpFinancePaymentDO::getUnitId, unitIds)
                .eqIfPresent(ErpFinancePaymentDO::getUnitId, reqVO.getUnitId())
                .orderByDesc(ErpFinancePaymentDO::getId);

        if (reqVO.getBizNo() != null) {
            query.leftJoin(ErpFinancePaymentItemDO.class, ErpFinancePaymentItemDO::getPaymentId, ErpFinancePaymentDO::getId)
                    .eq(reqVO.getBizNo() != null, ErpFinancePaymentItemDO::getBizNo, reqVO.getBizNo())
                    .groupBy(ErpFinancePaymentDO::getId); // 避免 1 对多查询，产生相同的 1
        }
        return selectJoinPage(reqVO, ErpFinancePaymentDO.class, query);
    }

    default int updateByIdAndStatus(Long id, Integer status, ErpFinancePaymentDO updateObj) {
        return update(updateObj, new LambdaUpdateWrapper<ErpFinancePaymentDO>()
                .eq(ErpFinancePaymentDO::getId, id)
                .eq(ErpFinancePaymentDO::getStatus, status)
                .in(ErpFinancePaymentDO::getUnitId, UnitUtil.getUnitIds())
        );
    }

    default ErpFinancePaymentDO selectByNo(String no) {
        return selectOne(new MPJLambdaWrapperX<ErpFinancePaymentDO>()
                .eq(ErpFinancePaymentDO::getNo, no)
                .in(ErpFinancePaymentDO::getUnitId, UnitUtil.getUnitIds())
        );
    }

}
