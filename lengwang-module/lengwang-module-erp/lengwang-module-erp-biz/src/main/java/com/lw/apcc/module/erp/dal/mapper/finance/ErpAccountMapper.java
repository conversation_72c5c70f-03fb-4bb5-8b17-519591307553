package com.lw.apcc.module.erp.dal.mapper.finance;

import com.lw.apcc.common.pojo.PageResult;
import com.lw.apcc.common.constants.IdConstants;
import com.lw.apcc.framework.mybatis.core.mapper.BaseMapperX;
import com.lw.apcc.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.lw.apcc.framework.mybatis.core.query.MPJLambdaWrapperX;
import com.lw.apcc.module.erp.controller.admin.finance.vo.account.ErpAccountPageReqVO;
import com.lw.apcc.module.erp.dal.dataobject.finance.ErpAccountDO;
import com.lw.apcc.module.erp.utils.UnitUtil;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;
import java.util.Set;

/**
 * ERP 结算账户 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface ErpAccountMapper extends BaseMapperX<ErpAccountDO> {

    default ErpAccountDO selectById(Long id) {
        return selectOne(new MPJLambdaWrapperX<ErpAccountDO>()
                .eq(ErpAccountDO::getId, id)
                .in(ErpAccountDO::getUnitId, UnitUtil.getUnitIds())
        );
    }

    default PageResult<ErpAccountDO> selectPage(ErpAccountPageReqVO reqVO) {

        List<Long> unitIds = UnitUtil.getUnitIds();
        // 是当前用户所属的经营主体的数据
        if (unitIds.isEmpty() || unitIds.contains(IdConstants.NON_EXISTENT_ID)) {
            return PageResult.empty();
        }

        return selectPage(reqVO, new LambdaQueryWrapperX<ErpAccountDO>()
                .likeIfPresent(ErpAccountDO::getName, reqVO.getName())
                .likeIfPresent(ErpAccountDO::getNo, reqVO.getNo())
                .eqIfPresent(ErpAccountDO::getRemark, reqVO.getRemark())
                .inIfPresent(ErpAccountDO::getUnitId, unitIds)
                .eqIfPresent(ErpAccountDO::getUnitId, reqVO.getUnitId())
                .orderByDesc(ErpAccountDO::getId));
    }

    default ErpAccountDO selectByDefaultStatus() {
        return selectOne(new MPJLambdaWrapperX<ErpAccountDO>()
                .eq(ErpAccountDO::getDefaultStatus, true)
                .in(ErpAccountDO::getUnitId, UnitUtil.getUnitIds()));
    }

    default List<ErpAccountDO> selectListByStatus(Integer status) {
        return selectList(new MPJLambdaWrapperX<ErpAccountDO>()
                .eq(ErpAccountDO::getStatus, status)
                .in(ErpAccountDO::getUnitId, UnitUtil.getUnitIds())
        );
    }

    default List<ErpAccountDO> listByAccountNames(Set<String> names) {
        return selectList(new MPJLambdaWrapperX<ErpAccountDO>()
                .in(ErpAccountDO::getName, names)
                .in(ErpAccountDO::getUnitId, UnitUtil.getUnitIds())
        );
    }
}
