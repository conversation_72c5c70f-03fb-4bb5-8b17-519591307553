package com.lw.apcc.module.erp.dal.mapper.purchase;

import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.lw.apcc.common.pojo.PageResult;
import com.lw.apcc.common.constants.IdConstants;
import com.lw.apcc.framework.mybatis.core.mapper.BaseMapperX;
import com.lw.apcc.framework.mybatis.core.query.MPJLambdaWrapperX;
import com.lw.apcc.module.erp.controller.admin.purchase.vo.order.ErpPurchaseOrderItemSimpleRespVO;
import com.lw.apcc.module.erp.controller.admin.purchase.vo.order.ErpPurchaseOrderPageReqVO;
import com.lw.apcc.module.erp.dal.dataobject.purchase.ErpPurchaseOrderDO;
import com.lw.apcc.module.erp.dal.dataobject.purchase.ErpPurchaseOrderItemDO;
import com.lw.apcc.module.erp.enums.ErpAuditStatus;
import com.lw.apcc.module.erp.utils.UnitUtil;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.io.Serializable;
import java.util.Collection;
import java.util.List;
import java.util.Objects;
import java.util.Set;

/**
 * ERP 采购订单 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface ErpPurchaseOrderMapper extends BaseMapperX<ErpPurchaseOrderDO> {

    /**
     * 获取单个采购订单
     *
     * @param id 编号
     * @return 采购订单
     */
    default ErpPurchaseOrderDO selectById(Long id) {
        // 是当前用户所属的经营主体的采购订单
        return selectOne(new MPJLambdaWrapperX<ErpPurchaseOrderDO>()
                .eq(ErpPurchaseOrderDO::getId, id)
                .in(ErpPurchaseOrderDO::getUnitId, UnitUtil.getUnitIds()));
    }

    @Override
    default List<ErpPurchaseOrderDO> selectBatchIds(Collection<? extends Serializable> ids) {
        return selectList(new MPJLambdaWrapperX<ErpPurchaseOrderDO>()
                .in(ErpPurchaseOrderDO::getId, ids)
                .in(ErpPurchaseOrderDO::getUnitId, UnitUtil.getUnitIds()));
    }

    /**
     * 分页查询采购订单
     *
     * @param reqVO 查询条件
     * @return 采购订单列表
     */
    default PageResult<ErpPurchaseOrderDO> selectPage(ErpPurchaseOrderPageReqVO reqVO) {

        List<Long> unitIds = UnitUtil.getUnitIds();
        // 是当前用户所属的经营主体的数据
        if (unitIds.isEmpty() || unitIds.contains(IdConstants.NON_EXISTENT_ID)) {
            return PageResult.empty();
        }

        MPJLambdaWrapperX<ErpPurchaseOrderDO> query = new MPJLambdaWrapperX<ErpPurchaseOrderDO>()
                .likeIfPresent(ErpPurchaseOrderDO::getNo, reqVO.getNo())
                .eqIfPresent(ErpPurchaseOrderDO::getSupplierId, reqVO.getSupplierId())
                .betweenIfPresent(ErpPurchaseOrderDO::getOrderTime, reqVO.getOrderTime())
                .eqIfPresent(ErpPurchaseOrderDO::getStatus, reqVO.getStatus())
                .likeIfPresent(ErpPurchaseOrderDO::getRemark, reqVO.getRemark())
                // 是当前用户所属的经营主体的采购订单
                .inIfPresent(ErpPurchaseOrderDO::getUnitId, unitIds)
                .eqIfPresent(ErpPurchaseOrderDO::getUnitId, reqVO.getUnitId())
                .eqIfPresent(ErpPurchaseOrderDO::getCreatorId, reqVO.getCreator())
                .orderByDesc(ErpPurchaseOrderDO::getId);

        // 入库状态。为什么需要 t. 的原因，是因为联表查询时，需要指定表名，不然会报 in_count 错误
        if (Objects.equals(reqVO.getInStatus(), ErpPurchaseOrderPageReqVO.IN_STATUS_NONE)) {
            query.eq(ErpPurchaseOrderDO::getInCount, 0);
        } else if (Objects.equals(reqVO.getInStatus(), ErpPurchaseOrderPageReqVO.IN_STATUS_PART)) {
            query.gt(ErpPurchaseOrderDO::getInCount, 0).apply("t.in_count < t.total_count");
        } else if (Objects.equals(reqVO.getInStatus(), ErpPurchaseOrderPageReqVO.IN_STATUS_ALL)) {
            query.apply("t.in_count = t.total_count");
        }
        // 退货状态
        if (Objects.equals(reqVO.getReturnStatus(), ErpPurchaseOrderPageReqVO.RETURN_STATUS_NONE)) {
            query.eq(ErpPurchaseOrderDO::getReturnCount, 0);
        } else if (Objects.equals(reqVO.getReturnStatus(), ErpPurchaseOrderPageReqVO.RETURN_STATUS_PART)) {
            query.gt(ErpPurchaseOrderDO::getReturnCount, 0).apply("t.return_count < t.total_count");
        } else if (Objects.equals(reqVO.getReturnStatus(), ErpPurchaseOrderPageReqVO.RETURN_STATUS_ALL)) {
            query.apply("t.return_count = t.total_count");
        }
        // 可采购入库
        if (Boolean.TRUE.equals(reqVO.getInEnable())) {
            query.eq(ErpPurchaseOrderDO::getStatus, ErpAuditStatus.APPROVE.getStatus())
                    .apply("t.in_count < t.total_count");
        }
        // 可采购退货
        if (Boolean.TRUE.equals(reqVO.getReturnEnable())) {
            query.eq(ErpPurchaseOrderDO::getStatus, ErpAuditStatus.APPROVE.getStatus())
                    .apply("t.return_count < t.in_count");
        }
        if (reqVO.getProductId() != null) {
            query.leftJoin(ErpPurchaseOrderItemDO.class, ErpPurchaseOrderItemDO::getOrderId, ErpPurchaseOrderDO::getId)
                    .eq(reqVO.getProductId() != null, ErpPurchaseOrderItemDO::getProductId, reqVO.getProductId())
                    .groupBy(ErpPurchaseOrderDO::getId); // 避免 1 对多查询，产生相同的 1
        }
        return selectJoinPage(reqVO, ErpPurchaseOrderDO.class, query);
    }

    /**
     * 获取采购订单项列表
     *
     * @param id 采购订单编号
     * @return 采购订单项列表
     */
    default int updateByIdAndStatus(Long id, Integer status, ErpPurchaseOrderDO updateObj) {
        return update(updateObj, new LambdaUpdateWrapper<ErpPurchaseOrderDO>()
                .eq(ErpPurchaseOrderDO::getId, id)
                .eq(ErpPurchaseOrderDO::getStatus, status)
                .in(ErpPurchaseOrderDO::getUnitId, UnitUtil.getUnitIds())
        );
    }

    /**
     * 通过订单号获取采购订单
     *
     * @param no 订单号
     * @return 采购订单
     */
    default ErpPurchaseOrderDO selectByNo(String no) {
        // 是当前用户所属的经营主体的采购订单
        return selectOne(new MPJLambdaWrapperX<ErpPurchaseOrderDO>()
                .eq(ErpPurchaseOrderDO::getNo, no)
                .in(ErpPurchaseOrderDO::getUnitId, UnitUtil.getUnitIds()));
    }

    List<ErpPurchaseOrderItemSimpleRespVO> listByOrderNoSimple(@Param("purchaseOrderNoSet") Set<String> purchaseOrderNoSet);

}
