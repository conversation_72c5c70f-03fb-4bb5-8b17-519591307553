package com.lw.apcc.module.erp.dal.mapper.finance;

import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.lw.apcc.common.pojo.PageResult;
import com.lw.apcc.common.constants.IdConstants;
import com.lw.apcc.framework.mybatis.core.mapper.BaseMapperX;
import com.lw.apcc.framework.mybatis.core.query.MPJLambdaWrapperX;
import com.lw.apcc.module.erp.controller.admin.finance.vo.receipt.ErpFinanceReceiptPageReqVO;
import com.lw.apcc.module.erp.dal.dataobject.finance.ErpFinanceReceiptDO;
import com.lw.apcc.module.erp.dal.dataobject.finance.ErpFinanceReceiptItemDO;
import com.lw.apcc.module.erp.utils.UnitUtil;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * ERP 收款单 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface ErpFinanceReceiptMapper extends BaseMapperX<ErpFinanceReceiptDO> {

    default ErpFinanceReceiptDO selectById(Long id) {
        // 是当前用户所属的经营主体的数据
        return selectOne(new MPJLambdaWrapperX<ErpFinanceReceiptDO>()
                .eq(ErpFinanceReceiptDO::getId, id)
                .in(ErpFinanceReceiptDO::getUnitId, UnitUtil.getUnitIds()));
    }

    default PageResult<ErpFinanceReceiptDO> selectPage(ErpFinanceReceiptPageReqVO reqVO) {

        List<Long> unitIds = UnitUtil.getUnitIds();
        // 是当前用户所属的经营主体的数据
        if (unitIds.isEmpty() || unitIds.contains(IdConstants.NON_EXISTENT_ID)) {
            return PageResult.empty();
        }

        MPJLambdaWrapperX<ErpFinanceReceiptDO> query = new MPJLambdaWrapperX<ErpFinanceReceiptDO>()
                .likeIfPresent(ErpFinanceReceiptDO::getNo, reqVO.getNo())
                .betweenIfPresent(ErpFinanceReceiptDO::getReceiptTime, reqVO.getReceiptTime())
                .eqIfPresent(ErpFinanceReceiptDO::getCustomerId, reqVO.getCustomerId())
                .eqIfPresent(ErpFinanceReceiptDO::getCreatorId, reqVO.getCreator())
                .eqIfPresent(ErpFinanceReceiptDO::getFinanceUserId, reqVO.getFinanceUserId())
                .eqIfPresent(ErpFinanceReceiptDO::getAccountId, reqVO.getAccountId())
                .eqIfPresent(ErpFinanceReceiptDO::getStatus, reqVO.getStatus())
                .likeIfPresent(ErpFinanceReceiptDO::getRemark, reqVO.getRemark())
                .inIfPresent(ErpFinanceReceiptDO::getUnitId, unitIds)
                .eqIfPresent(ErpFinanceReceiptDO::getUnitId, reqVO.getUnitId())
                .orderByDesc(ErpFinanceReceiptDO::getId);


        if (reqVO.getBizNo() != null) {
            query.leftJoin(ErpFinanceReceiptItemDO.class, ErpFinanceReceiptItemDO::getReceiptId, ErpFinanceReceiptDO::getId)
                    .eq(reqVO.getBizNo() != null, ErpFinanceReceiptItemDO::getBizNo, reqVO.getBizNo())
                    .groupBy(ErpFinanceReceiptDO::getId); // 避免 1 对多查询，产生相同的 1
        }
        return selectJoinPage(reqVO, ErpFinanceReceiptDO.class, query);
    }

    default int updateByIdAndStatus(Long id, Integer status, ErpFinanceReceiptDO updateObj) {
        return update(updateObj, new LambdaUpdateWrapper<ErpFinanceReceiptDO>()
                .eq(ErpFinanceReceiptDO::getId, id)
                .eq(ErpFinanceReceiptDO::getStatus, status)
                .in(ErpFinanceReceiptDO::getUnitId, UnitUtil.getUnitIds())
        );
    }

    default ErpFinanceReceiptDO selectByNo(String no) {
        return selectOne(new MPJLambdaWrapperX<ErpFinanceReceiptDO>()
                .eq(ErpFinanceReceiptDO::getNo, no)
                .in(ErpFinanceReceiptDO::getUnitId, UnitUtil.getUnitIds()));
    }

}
