package com.lw.apcc.module.erp.dal.mapper.product;

import com.lw.apcc.common.pojo.PageResult;
import com.lw.apcc.common.constants.IdConstants;
import com.lw.apcc.framework.mybatis.core.mapper.BaseMapperX;
import com.lw.apcc.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.lw.apcc.framework.mybatis.core.query.MPJLambdaWrapperX;
import com.lw.apcc.module.erp.controller.admin.product.vo.unit.ErpProductUnitPageReqVO;
import com.lw.apcc.module.erp.dal.dataobject.product.ErpProductUnitDO;
import com.lw.apcc.module.erp.utils.UnitUtil;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * ERP 产品单位 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface ErpProductUnitMapper extends BaseMapperX<ErpProductUnitDO> {

    default ErpProductUnitDO selectById(Long id) {
        // 是当前用户所属的经营主体的数据
        return selectOne(new MPJLambdaWrapperX<ErpProductUnitDO>()
                .eq(ErpProductUnitDO::getId, id)
                .in(ErpProductUnitDO::getUnitId, UnitUtil.getUnitIds()));
    }

    default PageResult<ErpProductUnitDO> selectPage(ErpProductUnitPageReqVO reqVO) {

        List<Long> unitIds = UnitUtil.getUnitIds();
        // 是当前用户所属的经营主体的数据
        if (unitIds.isEmpty() || unitIds.contains(IdConstants.NON_EXISTENT_ID)) {
            return PageResult.empty();
        }

        return selectPage(reqVO, new LambdaQueryWrapperX<ErpProductUnitDO>()
                .likeIfPresent(ErpProductUnitDO::getName, reqVO.getName())
                .eqIfPresent(ErpProductUnitDO::getStatus, reqVO.getStatus())
                .inIfPresent(ErpProductUnitDO::getUnitId, unitIds)
                .eqIfPresent(ErpProductUnitDO::getUnitId, reqVO.getUnitId())
                .orderByDesc(ErpProductUnitDO::getId));
    }

    default ErpProductUnitDO selectByName(String name) {
        return selectOne(new LambdaQueryWrapperX<ErpProductUnitDO>()
                .eq(ErpProductUnitDO::getName, name)
                .in(ErpProductUnitDO::getUnitId, UnitUtil.getUnitIds())
        );
    }

    default List<ErpProductUnitDO> selectListByStatus(Integer status) {
        // 是当前用户所属的经营主体的数据
        return selectList(new MPJLambdaWrapperX<ErpProductUnitDO>()
                .eq(ErpProductUnitDO::getStatus, status)
                .in(ErpProductUnitDO::getUnitId, UnitUtil.getUnitIds()));
    }

}
