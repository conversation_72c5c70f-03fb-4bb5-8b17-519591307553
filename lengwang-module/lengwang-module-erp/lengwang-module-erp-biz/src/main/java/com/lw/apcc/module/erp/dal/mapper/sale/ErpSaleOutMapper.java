package com.lw.apcc.module.erp.dal.mapper.sale;


import com.baomidou.mybatisplus.annotation.InterceptorIgnore;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.lw.apcc.common.pojo.PageResult;
import com.lw.apcc.common.util.collection.CollectionUtils;
import com.lw.apcc.common.constants.IdConstants;
import com.lw.apcc.framework.mybatis.core.mapper.BaseMapperX;
import com.lw.apcc.framework.mybatis.core.query.MPJLambdaWrapperX;
import com.lw.apcc.module.erp.controller.admin.dailystatistic.vo.ReceiptPaymentDailyStatisticReqVO;
import com.lw.apcc.module.erp.controller.admin.dailystatistic.vo.StockDailyStatisticReqVO;
import com.lw.apcc.module.erp.controller.admin.sale.vo.out.ErpSaleOutPageReqVO;
import com.lw.apcc.module.erp.dal.dataobject.sale.ErpSaleOutDO;
import com.lw.apcc.module.erp.dal.dataobject.sale.ErpSaleOutItemDO;
import com.lw.apcc.module.erp.enums.ErpAuditStatus;
import com.lw.apcc.module.erp.utils.UnitUtil;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;

/**
 * ERP 销售出库 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface ErpSaleOutMapper extends BaseMapperX<ErpSaleOutDO> {

    default ErpSaleOutDO selectById(Long id) {
        // 是当前用户所属的经营主体的数据
        return selectOne(new MPJLambdaWrapperX<ErpSaleOutDO>()
                .eq(ErpSaleOutDO::getId, id)
                .in(ErpSaleOutDO::getUnitId, UnitUtil.getUnitIds()));
    }

    @Override
    default List<ErpSaleOutDO> selectBatchIds(Collection<? extends Serializable> ids) {
        return selectList(new MPJLambdaWrapperX<ErpSaleOutDO>()
                .in(ErpSaleOutDO::getId, ids)
                .in(ErpSaleOutDO::getUnitId, UnitUtil.getUnitIds()));
    }

    default PageResult<ErpSaleOutDO> selectPage(ErpSaleOutPageReqVO reqVO) {

        List<Long> unitIds = UnitUtil.getUnitIds();
        // 是当前用户所属的经营主体的数据
        if (unitIds.isEmpty() || unitIds.contains(IdConstants.NON_EXISTENT_ID)) {
            return PageResult.empty();
        }

        MPJLambdaWrapperX<ErpSaleOutDO> query = new MPJLambdaWrapperX<ErpSaleOutDO>()
                .likeIfPresent(ErpSaleOutDO::getNo, reqVO.getNo())
                .eqIfPresent(ErpSaleOutDO::getCustomerId, reqVO.getCustomerId())
                .betweenIfPresent(ErpSaleOutDO::getOutTime, reqVO.getOutTime())
                .eqIfPresent(ErpSaleOutDO::getStatus, reqVO.getStatus())
                .likeIfPresent(ErpSaleOutDO::getRemark, reqVO.getRemark())
                .eqIfPresent(ErpSaleOutDO::getCreatorId, reqVO.getCreator())
                .eqIfPresent(ErpSaleOutDO::getAccountId, reqVO.getAccountId())
                .likeIfPresent(ErpSaleOutDO::getOrderNo, reqVO.getOrderNo())
                .inIfPresent(ErpSaleOutDO::getUnitId, unitIds)
                .eqIfPresent(ErpSaleOutDO::getUnitId, reqVO.getUnitId())
                .orderByDesc(ErpSaleOutDO::getId);

        // 收款状态。为什么需要 t. 的原因，是因为联表查询时，需要指定表名，不然会报字段不存在的错误
        if (Objects.equals(reqVO.getReceiptStatus(), ErpSaleOutPageReqVO.RECEIPT_STATUS_NONE)) {
            query.eq(ErpSaleOutDO::getReceiptPrice, 0);
        } else if (Objects.equals(reqVO.getReceiptStatus(), ErpSaleOutPageReqVO.RECEIPT_STATUS_PART)) {
            query.gt(ErpSaleOutDO::getReceiptPrice, 0).apply("t.receipt_price < t.total_price");
        } else if (Objects.equals(reqVO.getReceiptStatus(), ErpSaleOutPageReqVO.RECEIPT_STATUS_ALL)) {
            query.apply("t.receipt_price = t.total_price");
        }
        if (Boolean.TRUE.equals(reqVO.getReceiptEnable())) {
            query.eq(ErpSaleOutDO::getStatus, ErpAuditStatus.APPROVE.getStatus())
                    .apply("t.receipt_price < t.total_price");
        }
        if (reqVO.getWarehouseId() != null || reqVO.getProductId() != null) {
            query.leftJoin(ErpSaleOutItemDO.class, ErpSaleOutItemDO::getOutId, ErpSaleOutDO::getId)
                    .eq(reqVO.getWarehouseId() != null, ErpSaleOutItemDO::getWarehouseId, reqVO.getWarehouseId())
                    .eq(reqVO.getProductId() != null, ErpSaleOutItemDO::getProductId, reqVO.getProductId())
                    .groupBy(ErpSaleOutDO::getId); // 避免 1 对多查询，产生相同的 1
        }
        return selectJoinPage(reqVO, ErpSaleOutDO.class, query);
    }

    default int updateByIdAndStatus(Long id, Integer status, ErpSaleOutDO updateObj) {
        return update(updateObj, new LambdaUpdateWrapper<ErpSaleOutDO>()
                .eq(ErpSaleOutDO::getId, id)
                .eq(ErpSaleOutDO::getStatus, status)
                .in(ErpSaleOutDO::getUnitId, UnitUtil.getUnitIds())
        );
    }

    default ErpSaleOutDO selectByNo(String no) {
        return selectOne(new MPJLambdaWrapperX<ErpSaleOutDO>()
                .eq(ErpSaleOutDO::getNo, no)
                .in(ErpSaleOutDO::getUnitId, UnitUtil.getUnitIds()));
    }

    default List<ErpSaleOutDO> selectListByOrderId(Long orderId) {
        return selectList(new MPJLambdaWrapperX<ErpSaleOutDO>()
                .eq(ErpSaleOutDO::getOrderId, orderId)
                .in(ErpSaleOutDO::getUnitId, UnitUtil.getUnitIds()));
    }

    @InterceptorIgnore(tenantLine = "true")
    List<String> listBySaleOutNo(@Param("saleOutNos") Set<String> saleOutNos);

    default List<ErpSaleOutDO> listTotalSaleCount(StockDailyStatisticReqVO reqVO) {
        MPJLambdaWrapperX<ErpSaleOutDO> queryWrapper = new MPJLambdaWrapperX<>();
        queryWrapper.eq(ErpSaleOutDO::getStatus, ErpAuditStatus.APPROVE.getStatus())
                .eq(ErpSaleOutDO::getUnitId, reqVO.getUnitId())
                .ge(ErpSaleOutDO::getOutTime, reqVO.getStartDate())
                .le(ErpSaleOutDO::getOutTime, reqVO.getEndDate())
                .leftJoin(ErpSaleOutItemDO.class, ErpSaleOutItemDO::getOutId, ErpSaleOutDO::getId);

        // 检查 reqVO.getWarehouseId() 是否存在，动态添加条件
        if (reqVO.getWarehouseId() != null) {
            queryWrapper.eq(ErpSaleOutItemDO::getWarehouseId, reqVO.getWarehouseId());
        }

        return selectList(queryWrapper);
    }

    default PageResult<ErpSaleOutDO> pageSaleOutByDate(ReceiptPaymentDailyStatisticReqVO reqVO) {

        MPJLambdaWrapperX<ErpSaleOutDO> query = new MPJLambdaWrapperX<ErpSaleOutDO>()

                .betweenIfPresent(ErpSaleOutDO::getOutTime, reqVO.getStartDate(), reqVO.getEndDate())
                .eqIfPresent(ErpSaleOutDO::getStatus, ErpAuditStatus.APPROVE.getStatus())
                .eqIfPresent(ErpSaleOutDO::getUnitId, reqVO.getUnitId())
                .orderByDesc(ErpSaleOutDO::getCreateTime);


        if (reqVO.getWarehouseId() != null) {
            query.leftJoin(ErpSaleOutItemDO.class, ErpSaleOutItemDO::getOutId, ErpSaleOutDO::getId)
                    .eq(reqVO.getWarehouseId() != null, ErpSaleOutItemDO::getWarehouseId, reqVO.getWarehouseId())
                    .groupBy(ErpSaleOutDO::getId); // 避免 1 对多查询，产生相同的 1
        }
        return selectJoinPage(reqVO, ErpSaleOutDO.class, query);
    }

    /**
     * 获取所有出库量
     *
     * @param date
     * @return
     */
    default BigDecimal getStockOutTotal(LocalDateTime date) {
        List<ErpSaleOutDO> list = selectList(new MPJLambdaWrapperX<ErpSaleOutDO>()
                .eq(ErpSaleOutDO::getOutTime, date)
                .eq(ErpSaleOutDO::getStatus, ErpAuditStatus.APPROVE.getStatus())
        );

        return CollectionUtils.bigDecimalToSum(list, ErpSaleOutDO::getTotalCount);
    }

    /**
     * 获取最近几天的出库单
     *
     * @param days
     * @return
     */
    default List<ErpSaleOutDO> selectListByRecentlyDays(int days) {
        List<ErpSaleOutDO> list = selectList(
                Wrappers.<ErpSaleOutDO>lambdaQuery()
                        .ge(ErpSaleOutDO::getOutTime, LocalDate.now().minusDays(days - 1))
                        .eq(ErpSaleOutDO::getStatus, ErpAuditStatus.APPROVE.getStatus())
        );
        return list;
    }

}
