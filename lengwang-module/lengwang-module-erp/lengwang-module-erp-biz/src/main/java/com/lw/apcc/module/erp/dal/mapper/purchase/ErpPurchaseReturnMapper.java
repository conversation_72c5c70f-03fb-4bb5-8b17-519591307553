package com.lw.apcc.module.erp.dal.mapper.purchase;

import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.lw.apcc.common.pojo.PageResult;
import com.lw.apcc.common.constants.IdConstants;
import com.lw.apcc.framework.mybatis.core.mapper.BaseMapperX;
import com.lw.apcc.framework.mybatis.core.query.MPJLambdaWrapperX;
import com.lw.apcc.module.erp.controller.admin.dailystatistic.vo.ReceiptPaymentDailyStatisticReqVO;
import com.lw.apcc.module.erp.controller.admin.purchase.vo.returns.ErpPurchaseReturnPageReqVO;
import com.lw.apcc.module.erp.dal.dataobject.purchase.ErpPurchaseInDO;
import com.lw.apcc.module.erp.dal.dataobject.purchase.ErpPurchaseReturnDO;
import com.lw.apcc.module.erp.dal.dataobject.purchase.ErpPurchaseReturnItemDO;
import com.lw.apcc.module.erp.enums.ErpAuditStatus;
import com.lw.apcc.module.erp.utils.UnitUtil;
import org.apache.ibatis.annotations.Mapper;

import java.io.Serializable;
import java.util.Collection;
import java.util.List;
import java.util.Objects;

/**
 * ERP 采购退货 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface ErpPurchaseReturnMapper extends BaseMapperX<ErpPurchaseReturnDO> {

    /**
     * 获取单个采购退货
     *
     * @param id 编号
     * @return 采购退货
     */
    default ErpPurchaseReturnDO selectById(Long id) {
        // 是当前用户所属的经营主体的数据
        return selectOne(new MPJLambdaWrapperX<ErpPurchaseReturnDO>()
                .eq(ErpPurchaseReturnDO::getId, id)
                .in(ErpPurchaseReturnDO::getUnitId, UnitUtil.getUnitIds()));
    }

    @Override
    default List<ErpPurchaseReturnDO> selectBatchIds(Collection<? extends Serializable> ids) {
        return selectList(new MPJLambdaWrapperX<ErpPurchaseReturnDO>()
                .in(ErpPurchaseReturnDO::getId, ids)
                .in(ErpPurchaseReturnDO::getUnitId, UnitUtil.getUnitIds()));
    }

    default PageResult<ErpPurchaseReturnDO> selectPage(ErpPurchaseReturnPageReqVO reqVO) {

        List<Long> unitIds = UnitUtil.getUnitIds();
        // 是当前用户所属的经营主体的数据
        if (unitIds.isEmpty() || unitIds.contains(IdConstants.NON_EXISTENT_ID)) {
            return PageResult.empty();
        }

        MPJLambdaWrapperX<ErpPurchaseReturnDO> query = new MPJLambdaWrapperX<ErpPurchaseReturnDO>()
                .likeIfPresent(ErpPurchaseReturnDO::getNo, reqVO.getNo())
                .eqIfPresent(ErpPurchaseReturnDO::getSupplierId, reqVO.getSupplierId())
                .betweenIfPresent(ErpPurchaseReturnDO::getReturnTime, reqVO.getReturnTime())
                .eqIfPresent(ErpPurchaseReturnDO::getStatus, reqVO.getStatus())
                .likeIfPresent(ErpPurchaseReturnDO::getRemark, reqVO.getRemark())
                .eqIfPresent(ErpPurchaseReturnDO::getCreatorId, reqVO.getCreator())
                .eqIfPresent(ErpPurchaseReturnDO::getAccountId, reqVO.getAccountId())
                .likeIfPresent(ErpPurchaseReturnDO::getOrderNo, reqVO.getOrderNo())
                .inIfPresent(ErpPurchaseReturnDO::getUnitId, unitIds)
                .eqIfPresent(ErpPurchaseReturnDO::getUnitId, reqVO.getUnitId())
                .orderByDesc(ErpPurchaseReturnDO::getId);

        // 退款状态。为什么需要 t. 的原因，是因为联表查询时，需要指定表名，不然会报字段不存在的错误
        if (Objects.equals(reqVO.getRefundStatus(), ErpPurchaseReturnPageReqVO.REFUND_STATUS_NONE)) {
            query.eq(ErpPurchaseReturnDO::getRefundPrice, 0);
        } else if (Objects.equals(reqVO.getRefundStatus(), ErpPurchaseReturnPageReqVO.REFUND_STATUS_PART)) {
            query.gt(ErpPurchaseReturnDO::getRefundPrice, 0).apply("t.refund_price < t.total_price");
        } else if (Objects.equals(reqVO.getRefundStatus(), ErpPurchaseReturnPageReqVO.REFUND_STATUS_ALL)) {
            query.apply("t.refund_price = t.total_price");
        }
        if (Boolean.TRUE.equals(reqVO.getRefundEnable())) {
            query.eq(ErpPurchaseInDO::getStatus, ErpAuditStatus.APPROVE.getStatus())
                    .apply("t.refund_price < t.total_price");
        }
        if (reqVO.getWarehouseId() != null || reqVO.getProductId() != null) {
            query.leftJoin(ErpPurchaseReturnItemDO.class, ErpPurchaseReturnItemDO::getReturnId, ErpPurchaseReturnDO::getId)
                    .eq(reqVO.getWarehouseId() != null, ErpPurchaseReturnItemDO::getWarehouseId, reqVO.getWarehouseId())
                    .eq(reqVO.getProductId() != null, ErpPurchaseReturnItemDO::getProductId, reqVO.getProductId())
                    .groupBy(ErpPurchaseReturnDO::getId); // 避免 1 对多查询，产生相同的 1
        }
        return selectJoinPage(reqVO, ErpPurchaseReturnDO.class, query);
    }

    default int updateByIdAndStatus(Long id, Integer status, ErpPurchaseReturnDO updateObj) {
        return update(updateObj, new LambdaUpdateWrapper<ErpPurchaseReturnDO>()
                .eq(ErpPurchaseReturnDO::getId, id)
                .eq(ErpPurchaseReturnDO::getStatus, status)
                .in(ErpPurchaseReturnDO::getUnitId, UnitUtil.getUnitIds())
        );
    }

    default ErpPurchaseReturnDO selectByNo(String no) {
        // 是当前用户所属的经营主体的数据
        return selectOne(new MPJLambdaWrapperX<ErpPurchaseReturnDO>()
                .eq(ErpPurchaseReturnDO::getNo, no)
                .in(ErpPurchaseReturnDO::getUnitId, UnitUtil.getUnitIds()));
    }

    default List<ErpPurchaseReturnDO> selectListByOrderId(Long orderId) {
        // 是当前用户所属的经营主体的数据
        return selectList(new MPJLambdaWrapperX<ErpPurchaseReturnDO>()
                .eq(ErpPurchaseReturnDO::getOrderId, orderId)
                .in(ErpPurchaseReturnDO::getUnitId, UnitUtil.getUnitIds()));
    }

    default PageResult<ErpPurchaseReturnDO> pagePurchaseReturnByDate(ReceiptPaymentDailyStatisticReqVO reqVO) {

        MPJLambdaWrapperX<ErpPurchaseReturnDO> query = new MPJLambdaWrapperX<ErpPurchaseReturnDO>()

                .betweenIfPresent(ErpPurchaseReturnDO::getReturnTime, reqVO.getStartDate(), reqVO.getEndDate())
                .eqIfPresent(ErpPurchaseReturnDO::getStatus, ErpAuditStatus.APPROVE.getStatus())
                .eqIfPresent(ErpPurchaseReturnDO::getUnitId, reqVO.getUnitId())
                .orderByDesc(ErpPurchaseReturnDO::getCreateTime);

        if (reqVO.getWarehouseId() != null) {
            query.leftJoin(ErpPurchaseReturnItemDO.class, ErpPurchaseReturnItemDO::getReturnId, ErpPurchaseReturnDO::getId)
                    .eq(reqVO.getWarehouseId() != null, ErpPurchaseReturnItemDO::getWarehouseId, reqVO.getWarehouseId())
                    .groupBy(ErpPurchaseReturnDO::getId); // 避免 1 对多查询，产生相同的 1
        }
        return selectJoinPage(reqVO, ErpPurchaseReturnDO.class, query);
    }

}
