package com.lw.apcc.module.erp.service.stock;

import cn.hutool.core.date.DatePattern;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.lw.apcc.common.pojo.PageResult;
import com.lw.apcc.common.util.collection.CollectionUtils;
import com.lw.apcc.common.util.date.DateUtils;
import com.lw.apcc.common.constants.IdConstants;
import com.lw.apcc.framework.mybatis.core.util.MyBatisUtils;
import com.lw.apcc.module.erp.api.stock.dto.ProductCountRespDTO;
import com.lw.apcc.module.erp.api.stock.dto.StockInOutCountRespDTO;
import com.lw.apcc.module.erp.controller.admin.stock.vo.stock.ErpStockPageReqVO;
import com.lw.apcc.module.erp.dal.dataobject.stock.ErpStockDO;
import com.lw.apcc.module.erp.dal.dataobject.stock.ErpWarehouseDO;
import com.lw.apcc.module.erp.dal.mapper.stock.ErpStockMapper;
import com.lw.apcc.module.erp.dal.mapper.stock.ErpWarehouseMapper;
import com.lw.apcc.module.erp.service.product.ErpProductService;
import com.lw.apcc.module.erp.utils.UnitUtil;
import jakarta.annotation.Resource;
import org.dromara.hutool.core.date.DateUtil;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static com.lw.apcc.common.exception.util.ServiceExceptionUtil.exception;
import static com.lw.apcc.module.erp.enums.ErrorCodeConstants.STOCK_COUNT_NEGATIVE;
import static com.lw.apcc.module.erp.enums.ErrorCodeConstants.STOCK_COUNT_NEGATIVE2;

/**
 * ERP 产品库存 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class ErpStockServiceImpl implements ErpStockService {

    @Resource
    private ErpWarehouseMapper erpWarehouseMapper;

    /**
     * 允许库存为负数
     * <p>
     * TODO Lengwang：后续做成 db 配置
     */
    private static final Boolean NEGATIVE_STOCK_COUNT_ENABLE = false;

    @Resource
    private ErpProductService productService;
    @Resource
    private ErpWarehouseService warehouseService;

    @Resource
    private ErpStockMapper stockMapper;

    @Override
    public ErpStockDO getStock(Long id) {
        return stockMapper.selectById(id);
    }

    @Override
    public ErpStockDO getStock(Long productId, Long warehouseId) {
        return stockMapper.selectByProductIdAndWarehouseId(productId, warehouseId);
    }

    @Override
    public BigDecimal getStockCount(Long productId) {
        BigDecimal count = stockMapper.selectSumByProductId(productId);
        return count != null ? count : BigDecimal.ZERO;
    }

    @Override
    public PageResult<ErpStockDO> getStockPage(ErpStockPageReqVO pageReqVO) {

        List<Long> unitIds = UnitUtil.getUnitIds();
        // 是当前用户所属的经营主体的数据
        if (unitIds.isEmpty() || unitIds.contains(IdConstants.NON_EXISTENT_ID)) {
            return PageResult.empty();
        }
        IPage<ErpStockDO> page = stockMapper.pageStock(MyBatisUtils.buildPage(pageReqVO), pageReqVO, unitIds);

        return MyBatisUtils.buildPageResult(page);

        // return stockMapper.selectPage(pageReqVO);
    }

    @Override
    public BigDecimal updateStockCountIncrement(Long productId, Long warehouseId, BigDecimal count, Long unitId) {
        // 1.1 查询当前库存，如果库存是空的，初始化库存
        ErpStockDO stock = stockMapper.selectByProductIdAndWarehouseId(productId, warehouseId);
        if (stock == null) {
            stock = new ErpStockDO().setProductId(productId).setWarehouseId(warehouseId).setCount(BigDecimal.ZERO).setUnitId(unitId);
            stockMapper.insert(stock);
        }
        // 1.2 校验库存是否充足
        if (!NEGATIVE_STOCK_COUNT_ENABLE && stock.getCount().add(count).compareTo(BigDecimal.ZERO) < 0) {
            throw exception(STOCK_COUNT_NEGATIVE, productService.getProduct(productId).getName(),
                    warehouseService.getWarehouse(warehouseId).getName(), stock.getCount(), count);
        }

        // 2. 库存变更
        int updateCount = stockMapper.updateCountIncrement(stock.getId(), count, NEGATIVE_STOCK_COUNT_ENABLE);

        if (updateCount == 0) {
            // 此时不好去查询最新库存，所以直接抛出该提示，不提供具体库存数字
            throw exception(STOCK_COUNT_NEGATIVE2, productService.getProduct(productId).getName(), warehouseService.getWarehouse(warehouseId).getName());
        }

        // 3. 返回最新库存
        return stock.getCount().add(count);
    }

    /**
     * 查询某地区的产品库存总量
     *
     * @param areaPath 区域路径
     * @return 库存总量
     */
    @Override
    public BigDecimal getAreaCurrentStock(String areaPath) {
        Float stock = stockMapper.getAreaCurrentStock(areaPath);
        return ObjectUtil.isNotEmpty(stock) ? new BigDecimal(Float.toString(stock)) : BigDecimal.ZERO;
    }

    /**
     * 查询某地区的产品库存总量
     *
     * @param areaPath 区域路径
     * @return 仓储产品占比
     */
    @Override
    public List<ProductCountRespDTO> getProductProportion(String areaPath, Long tenantId) {
        List<ProductCountRespDTO> productCountList = stockMapper.getProductProportion(areaPath, tenantId);

        // 计算各类产品数占总的比例
        BigDecimal total = productCountList.stream().map(ProductCountRespDTO::getCount).reduce(BigDecimal.ZERO, BigDecimal::add);
        if (total.compareTo(BigDecimal.ZERO) > 0) {
            productCountList.forEach(productCount -> productCount.setCount(productCount.getCount().divide(total, 2, BigDecimal.ROUND_HALF_UP)));
        }

        return productCountList;
    }

    /**
     * 查询某地区某时间段内的出入库
     *
     * @param areaPath 区域Path
     * @return 出入库
     */
    @Override
    public List<StockInOutCountRespDTO> getAreaStockInOutCount(String areaPath) {

        String endDate = DateTimeFormatter.ofPattern(DatePattern.NORM_DATE_PATTERN).format(LocalDate.now());
        String startDate = DateUtils.add(endDate, -6);

        // 查询出入库
        List<StockInOutCountRespDTO> stockInCountDTO = stockMapper.getAreaStockInCount(areaPath, startDate);
        List<StockInOutCountRespDTO> stockOutCountDTO = stockMapper.getAreaStockOutCount(areaPath, startDate);

        Map<String, Long> inCountMap = stockInCountDTO.stream().collect(Collectors.toMap(StockInOutCountRespDTO::getDate, StockInOutCountRespDTO::getStockInCount));
        Map<String, Long> outCountMap = stockOutCountDTO.stream().collect(Collectors.toMap(StockInOutCountRespDTO::getDate, StockInOutCountRespDTO::getStockOutCount));

        // 合并结果
        List<String> timeList = DateUtils.getTimeList(startDate, endDate);
        List<StockInOutCountRespDTO> stockInOutCountList = CollectionUtils.toLinkedList(timeList, time -> {

            StockInOutCountRespDTO stockInOutDTO = new StockInOutCountRespDTO();
            stockInOutDTO.setDate(DateUtil.format(DateUtil.parse(time), "MM.dd"));
            stockInOutDTO.setStockInCount(inCountMap.getOrDefault(time, 0L));
            stockInOutDTO.setStockOutCount(outCountMap.getOrDefault(time, 0L));

            return stockInOutDTO;
        });

        return stockInOutCountList;
    }

    @Override
    public List<Long> getAssetIdsBatchWarehouse() {
        List<Long> result = erpWarehouseMapper.selectList().stream()
                .filter(warehouse -> warehouse.getAssetId() != null)
                .map(ErpWarehouseDO::getAssetId).distinct().toList();

        return result;
    }

}
