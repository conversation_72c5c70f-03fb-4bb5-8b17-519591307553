package com.lw.apcc.module.erp.dal.mapper.purchase;

import com.lw.apcc.common.pojo.PageResult;
import com.lw.apcc.common.constants.IdConstants;
import com.lw.apcc.framework.mybatis.core.mapper.BaseMapperX;
import com.lw.apcc.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.lw.apcc.framework.mybatis.core.query.MPJLambdaWrapperX;
import com.lw.apcc.module.erp.controller.admin.purchase.vo.supplier.ErpSupplierPageReqVO;
import com.lw.apcc.module.erp.dal.dataobject.purchase.ErpSupplierDO;
import com.lw.apcc.module.erp.utils.UnitUtil;
import org.apache.ibatis.annotations.Mapper;

import java.io.Serializable;
import java.util.Collection;
import java.util.List;
import java.util.Set;

/**
 * ERP 供应商 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface ErpSupplierMapper extends BaseMapperX<ErpSupplierDO> {


    /**
     * 获取单个供应商
     *
     * @param id 编号
     * @return 供应商
     */
    default ErpSupplierDO selectById(Long id) {
        // 是当前用户所属的经营主体的数据
        return selectOne(new MPJLambdaWrapperX<ErpSupplierDO>()
                .eq(ErpSupplierDO::getId, id)
                .in(ErpSupplierDO::getUnitId, UnitUtil.getUnitIds()));
    }

    @Override
    default List<ErpSupplierDO> selectBatchIds(Collection<? extends Serializable> ids) {
        return selectList(new MPJLambdaWrapperX<ErpSupplierDO>()
                .in(ErpSupplierDO::getId, ids)
                .in(ErpSupplierDO::getUnitId, UnitUtil.getUnitIds()));
    }

    default PageResult<ErpSupplierDO> selectPage(ErpSupplierPageReqVO reqVO) {

        List<Long> unitIds = UnitUtil.getUnitIds();
        // 是当前用户所属的经营主体的数据
        if (unitIds.isEmpty() || unitIds.contains(IdConstants.NON_EXISTENT_ID)) {
            return PageResult.empty();
        }

        return selectPage(reqVO, new LambdaQueryWrapperX<ErpSupplierDO>()
                .likeIfPresent(ErpSupplierDO::getName, reqVO.getName())
                .likeIfPresent(ErpSupplierDO::getMobile, reqVO.getMobile())
                .likeIfPresent(ErpSupplierDO::getTelephone, reqVO.getTelephone())
                .inIfPresent(ErpSupplierDO::getUnitId, unitIds)
                .eqIfPresent(ErpSupplierDO::getUnitId, reqVO.getUnitId())
                .orderByDesc(ErpSupplierDO::getId));
    }

    default List<ErpSupplierDO> selectListByStatus(Integer status) {
        // 是当前用户所属的经营主体的数据
        return selectList(new LambdaQueryWrapperX<ErpSupplierDO>()
                .eq(ErpSupplierDO::getStatus, status)
                .in(ErpSupplierDO::getUnitId, UnitUtil.getUnitIds()));
    }

    default List<ErpSupplierDO> listSupplierByNames(Set<String> supplierNames) {
        return selectList(new LambdaQueryWrapperX<ErpSupplierDO>()
                .in(ErpSupplierDO::getName, supplierNames)
                .in(ErpSupplierDO::getUnitId, UnitUtil.getUnitIds()));
    }

}
