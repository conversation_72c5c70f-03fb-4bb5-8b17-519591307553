package com.lw.apcc.module.erp.dal.mapper.product;

import com.lw.apcc.common.pojo.PageResult;
import com.lw.apcc.common.constants.IdConstants;
import com.lw.apcc.framework.mybatis.core.mapper.BaseMapperX;
import com.lw.apcc.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.lw.apcc.framework.mybatis.core.query.MPJLambdaWrapperX;
import com.lw.apcc.module.erp.controller.admin.product.vo.product.ErpProductPageReqVO;
import com.lw.apcc.module.erp.dal.dataobject.product.ErpProductDO;
import com.lw.apcc.module.erp.utils.UnitUtil;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;
import java.util.Set;

/**
 * ERP 产品 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface ErpProductMapper extends BaseMapperX<ErpProductDO> {

    /**
     * 获取单个产品
     *
     * @param id 编号
     * @return 产品
     */
    default ErpProductDO selectById(Long id) {
        // 是当前用户所属的经营主体的数据
        return selectOne(new MPJLambdaWrapperX<ErpProductDO>()
                .eq(ErpProductDO::getId, id)
                .in(ErpProductDO::getUnitId, UnitUtil.getUnitIds()));
    }

    default PageResult<ErpProductDO> selectPage(ErpProductPageReqVO reqVO) {

        List<Long> unitIds = UnitUtil.getUnitIds();
        // 是当前用户所属的经营主体的数据
        if (unitIds.isEmpty() || unitIds.contains(IdConstants.NON_EXISTENT_ID)) {
            return PageResult.empty();
        }

        return selectPage(reqVO, new LambdaQueryWrapperX<ErpProductDO>()
                .likeIfPresent(ErpProductDO::getName, reqVO.getName())
                .eqIfPresent(ErpProductDO::getCategoryId, reqVO.getCategoryId())
                .betweenIfPresent(ErpProductDO::getCreateTime, reqVO.getCreateTime())
                .inIfPresent(ErpProductDO::getUnitId, unitIds)
                .eqIfPresent(ErpProductDO::getUnitId, reqVO.getUnitId())
                .orderByDesc(ErpProductDO::getId));
    }

    default Long selectCountByCategoryId(Long categoryId) {
        return selectCount(new MPJLambdaWrapperX<ErpProductDO>()
                .eq(ErpProductDO::getCategoryId, categoryId)
                .in(ErpProductDO::getUnitId, UnitUtil.getUnitIds())
        );

    }

    default Long selectCountByUnitId(Long unitId) {
        return selectCount(new MPJLambdaWrapperX<ErpProductDO>()
                .eq(ErpProductDO::getUnitId, unitId)
                .in(ErpProductDO::getUnitId, UnitUtil.getUnitIds())
        );
    }

    default List<ErpProductDO> getProductByNames(Set<String> productNames) {
return selectList(new LambdaQueryWrapperX<ErpProductDO>()
                .in(ErpProductDO::getName, productNames)
                .in(ErpProductDO::getUnitId, UnitUtil.getUnitIds()));
    }

    default List<ErpProductDO> selectListByStatus(Integer status) {
        // 是当前用户所属的经营主体的数据
        return selectList(new MPJLambdaWrapperX<ErpProductDO>()
                .eq(ErpProductDO::getStatus, status)
                .in(ErpProductDO::getUnitId, UnitUtil.getUnitIds()));
    }

}
