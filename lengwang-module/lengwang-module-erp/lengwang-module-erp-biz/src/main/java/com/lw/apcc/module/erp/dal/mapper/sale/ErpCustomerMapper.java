package com.lw.apcc.module.erp.dal.mapper.sale;

import com.lw.apcc.common.pojo.PageResult;
import com.lw.apcc.common.constants.IdConstants;
import com.lw.apcc.framework.mybatis.core.mapper.BaseMapperX;
import com.lw.apcc.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.lw.apcc.framework.mybatis.core.query.MPJLambdaWrapperX;
import com.lw.apcc.module.erp.controller.admin.sale.vo.customer.ErpCustomerPageReqVO;
import com.lw.apcc.module.erp.dal.dataobject.sale.ErpCustomerDO;
import com.lw.apcc.module.erp.utils.UnitUtil;
import org.apache.ibatis.annotations.Mapper;

import java.io.Serializable;
import java.util.Collection;
import java.util.List;

/**
 * ERP 客户 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface ErpCustomerMapper extends BaseMapperX<ErpCustomerDO> {

    default ErpCustomerDO selectById(Long id) {
        // 是当前用户所属的经营主体的数据
        return selectOne(new MPJLambdaWrapperX<ErpCustomerDO>()
                .eq(ErpCustomerDO::getId, id)
                .in(ErpCustomerDO::getUnitId, UnitUtil.getUnitIds()));
    }

    @Override
    default List<ErpCustomerDO> selectBatchIds(Collection<? extends Serializable> ids) {
        return selectList(new MPJLambdaWrapperX<ErpCustomerDO>()
                .in(ErpCustomerDO::getId, ids)
                .in(ErpCustomerDO::getUnitId, UnitUtil.getUnitIds()));
    }

    default PageResult<ErpCustomerDO> selectPage(ErpCustomerPageReqVO reqVO) {

        List<Long> unitIds = UnitUtil.getUnitIds();
        // 是当前用户所属的经营主体的数据
        if (unitIds.isEmpty() || unitIds.contains(IdConstants.NON_EXISTENT_ID)) {
            return PageResult.empty();
        }

        return selectPage(reqVO, new LambdaQueryWrapperX<ErpCustomerDO>()
                .likeIfPresent(ErpCustomerDO::getName, reqVO.getName())
                .eqIfPresent(ErpCustomerDO::getMobile, reqVO.getMobile())
                .eqIfPresent(ErpCustomerDO::getTelephone, reqVO.getTelephone())
                .inIfPresent(ErpCustomerDO::getUnitId, unitIds)
                .eqIfPresent(ErpCustomerDO::getUnitId, reqVO.getUnitId())
                .orderByDesc(ErpCustomerDO::getId));
    }

    default List<ErpCustomerDO> selectListByStatus(Integer status) {
        // 是当前用户所属的经营主体的数据
        return selectList(new LambdaQueryWrapperX<ErpCustomerDO>()
                .eq(ErpCustomerDO::getStatus, status)
                .in(ErpCustomerDO::getUnitId, UnitUtil.getUnitIds()));
    }

}
