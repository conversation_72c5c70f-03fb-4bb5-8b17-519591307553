<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lw.apcc.module.coldchain.dal.mapper.assets.AssetCodeScanLogMapper">

    <!--
        一般情况下，尽可能使用 Mapper 进行 CRUD 增删改查即可。
        无法满足的场景，例如说多表关联查询，才使用 XML 编写 SQL。
        代码生成器暂时只生成 Mapper XML 文件本身，更多推荐 MybatisX 快速开发插件来生成查询。
     -->

    <select id="pageByAssetCodeScanLog" resultType="com.lw.apcc.module.coldchain.controller.admin.assets.vo.AssetCodeScanLogRespVO">
        select
            acsl.id,
            acsl.code,
            acsl.user_id,
            su.nickname as userName,
            acsl.unit_id,
            u.name as unitName,
            acsl.asset_id,
            a.name as assetName,
            acsl.longitude,
            acsl.latitude,
            acsl.create_time
        from
            coldchain_asset_code_scan_log acsl
            left join system_users su on acsl.user_id = su.id and su.deleted = 0
            left join coldchain_units u on acsl.unit_id = u.id and u.status = 1 and u.deleted = 0
            left join coldchain_assets a on acsl.asset_id = a.id and a.status = 1 and a.deleted = 0
        where
            acsl.deleted = 0
            <if test="pageReqVO.code != null">
                and acsl.code = #{pageReqVO.code}
            </if>
            <if test="pageReqVO.userId != null">
                and acsl.user_id = #{pageReqVO.userId}
            </if>
            <if test="pageReqVO.unitId != null">
                and acsl.unit_id = #{pageReqVO.unitId}
            </if>
            <if test="pageReqVO.assetId != null">
                and acsl.asset_id = #{pageReqVO.assetId}
            </if>
            <if test="pageReqVO.longitude != null and pageReqVO.longitude != ''">
                and acsl.longitude = #{pageReqVO.longitude}
            </if>
            <if test="pageReqVO.latitude != null and pageReqVO.latitude != ''">
                and acsl.latitude = #{pageReqVO.latitude}
            </if>
            <if test="pageReqVO.createTime != null and pageReqVO.createTime.length == 2">
                and acsl.create_time between #{pageReqVO.createTime[0]} and #{pageReqVO.createTime[1]}
            </if>

            <!--数据权限-->
            <include refid="com.lw.apcc.module.coldchain.dal.mapper.units.UnitsMapper.sqlWhereByDataPermission"></include>
        order by
            acsl.create_time desc
    </select>

</mapper>
