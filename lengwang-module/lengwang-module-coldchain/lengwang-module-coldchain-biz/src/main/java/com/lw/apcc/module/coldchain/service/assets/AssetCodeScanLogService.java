package com.lw.apcc.module.coldchain.service.assets;

import jakarta.validation.*;
import com.lw.apcc.framework.mybatis.core.service.BaseService;
import com.lw.apcc.module.coldchain.controller.admin.assets.vo.*;
import com.lw.apcc.module.coldchain.dal.dataobject.assets.AssetCodeScanLogDO;
import com.lw.apcc.common.pojo.PageResult;

/**
 * 一库一码扫码记录信息 Service 接口
 *
 * <AUTHOR>
 */
public interface AssetCodeScanLogService extends BaseService<AssetCodeScanLogDO> {

    /**
     * 创建一库一码扫码记录信息
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createAssetCodeScanLog(@Valid AssetCodeScanLogSaveReqVO createReqVO);

    /**
     * 更新一库一码扫码记录信息
     *
     * @param updateReqVO 更新信息
     */
    void updateAssetCodeScanLog(@Valid AssetCodeScanLogSaveReqVO updateReqVO);

    /**
     * 删除一库一码扫码记录信息
     *
     * @param id 编号
     */
    void deleteAssetCodeScanLog(Long id);

    /**
     * 获得一库一码扫码记录信息
     *
     * @param id 编号
     * @return 一库一码扫码记录信息
     */
    AssetCodeScanLogDO getAssetCodeScanLog(Long id);

    /**
     * 获得一库一码扫码记录信息分页
     *
     * @param pageReqVO 分页查询
     * @return 一库一码扫码记录信息分页
     */
    PageResult<AssetCodeScanLogRespVO> getAssetCodeScanLogPage(AssetCodeScanLogPageReqVO pageReqVO);

}
