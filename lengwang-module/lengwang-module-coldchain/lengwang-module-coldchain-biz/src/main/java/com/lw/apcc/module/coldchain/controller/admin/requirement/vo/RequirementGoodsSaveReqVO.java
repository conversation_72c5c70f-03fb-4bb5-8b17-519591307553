package com.lw.apcc.module.coldchain.controller.admin.requirement.vo;

import com.alibaba.fastjson.JSONArray;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDate;

/**
 * <AUTHOR>
 */
@Schema(description = "管理后台 - 冷链共享 - 货源求购信息 新增/修改 Request VO")
@Data
public class RequirementGoodsSaveReqVO {

    @Schema(description = "主键ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "19331")
    private Long id;

    @Schema(description = "求购主键ID", example = "26727")
    private Long requirementId;

    @Schema(description = "求租模型（1库、2车、3货）（字典：coldchain_requirement_model）")
    @NotNull(message = "求租模型不能为空")
    private Integer requirementModel;

    @Schema(description = "单位ID", example = "24675")
    private Long unitId;

    @Schema(description = "区域ID", example = "23140")
    @NotNull(message = "区域ID不能为空")
    private Long areaId;

    @Schema(description = "区域PATH")
    @NotNull(message = "区域PATH不能为空")
    private String areaPath;

    @Schema(description = "详细地址")
    private String address;

    @Schema(description = "经度")
    private String longitude;

    @Schema(description = "纬度")
    private String latitude;

    @Schema(description = "附近范围（附近1公里，2公里，3公里）（字典：coldchain_requirement_near_type）", example = "2")
    private Integer nearType;

    @Schema(description = "标题", example = "Lengwang")
    @NotNull(message = "标题不能为空")
    private String name;

    @Schema(description = "描述", example = "你说的对")
    private String description;

    @Schema(description = "联系人")
    @NotNull(message = "联系人不能为空")
    private String contact;

    @Schema(description = "联系电话")
    @NotNull(message = "联系电话不能为空")
    private String mobile;

    @Schema(description = "审核状态（字典：coldchain_lease_approve_status）", example = "1")
    private Integer approveStatus;

    @Schema(description = "求租状态（字典：coldchain_requirement_status）", example = "2")
    private Integer requirementStatus;

    @Schema(description = "状态", example = "2")
    private Integer status;

    @Schema(description = "过期时间")
    private LocalDate expireDate;

    @Schema(description = "货物种类（字典：coldchain_lease_goods_type）", example = "2")
    private Integer goodsType;

    @Schema(description = "货品小类（对应小类表）")
    private Long goodsCategory;

    @Schema(description = "图片描述")
    private JSONArray image;

    @Schema(description = "求购数量")
    private BigDecimal quantity;

    @Schema(description = "求购单价", example = "6920")
    private BigDecimal price;

    @Schema(description = "价格单位（字典：coldchain_lease_price_unit）")
    private String priceUnit;

    @Schema(description = "收件人姓名", example = "赵六")
    private String reciveUserName;

    @Schema(description = "收件人手机号")
    private String reciveUserMobile;

    @Schema(description = "收件人地址")
    private String reciveUserAddress;

}