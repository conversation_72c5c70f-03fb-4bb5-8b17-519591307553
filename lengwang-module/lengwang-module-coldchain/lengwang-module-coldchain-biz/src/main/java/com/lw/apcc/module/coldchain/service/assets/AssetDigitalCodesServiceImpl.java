package com.lw.apcc.module.coldchain.service.assets;

import cn.hutool.core.io.IoUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.qrcode.QrCodeUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.lw.apcc.common.enums.DefaultStatusEnum;
import com.lw.apcc.common.pojo.PageResult;
import com.lw.apcc.common.security.SecurityUtils;
import com.lw.apcc.common.util.area.AreaUtils;
import com.lw.apcc.common.util.collection.CollectionUtils;
import com.lw.apcc.common.util.date.DateUtils;
import com.lw.apcc.common.util.http.HttpUtils;
import com.lw.apcc.common.util.object.BeanUtils;
import com.lw.apcc.common.util.pdf.dto.PdfPropertyDTO;
import com.lw.apcc.common.util.pdf.enums.EPdfProperty;
import com.lw.apcc.common.util.pdf.util.TablePdfPropertyUtil;
import com.lw.apcc.common.util.pdf.util.TemplatePdfUtil;
import com.lw.apcc.common.util.rate.RateUtil;
import com.lw.apcc.common.util.string.StringUtil;
import com.lw.apcc.common.util.zip.ZipUtils;
import com.lw.apcc.framework.dict.core.cache.DictCaChe;
import com.lw.apcc.framework.mybatis.core.service.BaseServiceImpl;
import com.lw.apcc.framework.mybatis.core.util.MyBatisUtils;
import com.lw.apcc.framework.redis.utils.RedisCacheUtil;
import com.lw.apcc.framework.redis.utils.RedisLockUtil;
import com.lw.apcc.module.coldchain.api.assets.dto.AssetsDigitalCodeConfigRespDTO;
import com.lw.apcc.module.coldchain.api.assets.dto.AssetsDigitalCodeRespDTO;
import com.lw.apcc.module.coldchain.cache.AssetDigitalCodeConfigCache;
import com.lw.apcc.module.coldchain.controller.admin.assets.vo.*;
import com.lw.apcc.module.coldchain.controller.admin.statistics.vo.AssetCodeTotalStatisticsRespVO;
import com.lw.apcc.module.coldchain.controller.admin.units.vo.UnitImagesRespVO;
import com.lw.apcc.module.coldchain.controller.admin.units.vo.UnitsDetailsRespVO;
import com.lw.apcc.module.coldchain.controller.admin.validationReport.vo.ValidationReportRespVO;
import com.lw.apcc.module.coldchain.controller.app.assets.vo.AppAssetDigitalCodesBindReqVO;
import com.lw.apcc.module.coldchain.convert.assets.AssetCodePrintLogConvert;
import com.lw.apcc.module.coldchain.dal.dataobject.assets.*;
import com.lw.apcc.module.coldchain.dal.dataobject.units.UnitImagesDO;
import com.lw.apcc.module.coldchain.dal.dataobject.units.UnitsDO;
import com.lw.apcc.module.coldchain.dal.dataobject.validationReport.ValidationReportDO;
import com.lw.apcc.module.coldchain.dal.mapper.assets.AssetCodePhotoLogsMapper;
import com.lw.apcc.module.coldchain.dal.mapper.assets.AssetDigitalCodesMapper;
import com.lw.apcc.module.coldchain.dal.mapper.assets.AssetGradeMonthlyStatisticMapper;
import com.lw.apcc.module.coldchain.dal.mapper.assets.AssetsMapper;
import com.lw.apcc.module.coldchain.dal.mapper.lease.LeaseGoodsMapper;
import com.lw.apcc.module.coldchain.dal.mapper.lease.LeaseWarehouseMapper;
import com.lw.apcc.module.coldchain.dal.mapper.units.UnitImagesMapper;
import com.lw.apcc.module.coldchain.dal.mapper.units.UnitsMapper;
import com.lw.apcc.module.coldchain.dal.mapper.validationReport.ValidationReportMapper;
import com.lw.apcc.module.coldchain.enums.DictTypeConstants;
import com.lw.apcc.module.coldchain.enums.assets.AssetCodePhotoTypeConstants;
import com.lw.apcc.module.coldchain.convert.assets.AssetDigitalCodesConvert;
import com.lw.apcc.module.coldchain.convert.assets.AssetsConvert;
import com.lw.apcc.module.coldchain.convert.units.UnitImagesConvert;
import com.lw.apcc.module.coldchain.service.assets.bo.AssetDigitalCodePdfBO;
import com.lw.apcc.module.coldchain.util.AssetDigitalCodeUtil;
import com.lw.apcc.module.infra.api.file.FileApi;
import com.lw.apcc.module.iot.api.assets.AssetRelApi;
import com.lw.apcc.module.system.api.area.dto.AreaRespDTO;
import com.lw.apcc.module.system.api.permission.dto.DataPermissionRespDTO;
import com.lw.apcc.module.system.cache.AreaCache;
import com.lw.apcc.module.system.cache.DataPermissionCache;
import com.lw.apcc.module.system.constants.AreaConstants;
import com.lw.apcc.module.system.enums.permission.DataPermissionCodeEnum;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionSynchronizationAdapter;
import org.springframework.transaction.support.TransactionSynchronizationManager;
import org.springframework.validation.annotation.Validated;

import javax.imageio.ImageIO;
import java.awt.image.BufferedImage;
import java.io.ByteArrayOutputStream;
import java.io.InputStream;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.stream.Collectors;

import static com.lw.apcc.common.exception.util.ServiceExceptionUtil.exception;
import static com.lw.apcc.module.coldchain.enums.AssetDigitalCodesConstants.ASSET_DIGITAL_CODE_PDF_FILE_MODEL;
import static com.lw.apcc.module.coldchain.enums.AssetDigitalCodesConstants.ASSET_DIGITAL_CODE_URL;
import static com.lw.apcc.module.coldchain.enums.ErrorCodeConstants.*;

/**
 * 一库一码 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
@Slf4j
@EnableAsync
public class AssetDigitalCodesServiceImpl extends BaseServiceImpl<AssetDigitalCodesMapper, AssetDigitalCodesDO> implements AssetDigitalCodesService {

    private static final Logger logger = LoggerFactory.getLogger(AssetDigitalCodesService.class);

    private static final int MAX_SERIAL = 9999;

    private static final int MAX_MONTH_LOOKAHEAD = 12; // 最多向后找12个月

    private static final String ASSET_DIGITAL_CODE_SERIAL_REDIS_KEY = "ASSET_DIGITAL_CODE_SERIAL:"; // 最多向后找12个月

    @Resource
    private UnitsMapper unitsMapper;

    @Resource
    private UnitImagesMapper unitImagesMapper;

    @Resource
    private AssetsMapper assetsMapper;

    @Resource
    private AssetCodePhotoLogsMapper assetCodePhotoLogsMapper;

    @Resource
    private AssetHasAttributesService attributesService;

    @Resource
    private LeaseWarehouseMapper leaseWarehouseMapper;

    @Resource
    private LeaseGoodsMapper leaseGoodsMapper;

    @Resource
    private FileApi fileApi;

    @Resource
    private AssetGradeMonthlyStatisticMapper assetGradeMonthlyStatisticMapper;

    @Resource
    private ValidationReportMapper validationReportMapper;

    @Resource
    private AssetRelApi assetRelApi;

    @Resource
    private AssetCodePrintLogService assetCodePrintLogService;

    @Resource
    private AssetCodeScanLogService assetCodeScanLogService;

    @Override
    public AssetDigitalCodesDO getAssetDigitalCodes(Long id) {
        return baseMapper.selectById(id);
    }

    /**
     * 一库一码详情
     *
     * @param assetId
     * @return
     */
    @Override
    public AssetDigitalCodeDetailRespVO getCodeDetailByAssetId(Long assetId) {
        return this.getCodeDetailByAssetId(assetId, null);
    }

    /**
     * 一库一码详情
     *
     * @param code
     * @return
     */
    @Override
    public AssetDigitalCodeDetailRespVO getCodeDetailByCode(String code) {
        if(code.equals("64012210020306010")){
            code = "360404250400002";
        }

        return this.getCodeDetailByAssetId(null, code);
    }

    /**
     * 一库一码详情
     *
     * @param assetId
     * @return
     */
    @Override
    public AssetDigitalCodeDetailRespVO getCodeDetailByAssetId(Long assetId, String code) {

        // 基础信息
        AssetDigitalCodeDetailRespVO respVO = baseMapper.getCodeDetailByAssetId(assetId, code);
        if (respVO == null) {
            throw exception(ASSET_DIGITAL_CODES_NOT_EXISTS);
        }

        // 单位信息
        AssetDigitalCodeUnitRespVO unit = getAssetDigitalCodeUnit(respVO.getUnitId());
        respVO.setUnit(unit);

        // 冷库资产
        AssetDigitalCodeAssetRespVO asset = assetsMapper.getDigitalCodeAssetById(respVO.getAssetId());
        initAssetDigitalCodeAsset(asset);
        respVO.setAsset(asset);

        // 经纬度
        respVO.setLatitude(ObjectUtil.defaultIfBlank(asset.getLatitude(), unit.getLatitude()));
        respVO.setLongitude(ObjectUtil.defaultIfBlank(asset.getLongitude(), unit.getLongitude()));

        // 其他冷库资产集合
        List<AssetDigitalCodeAssetRespVO> otherWarehouseList = assetsMapper.listOtherDigitalCodeWarehouseAssetById(respVO.getAssetId(), respVO.getUnitId());
        initAssetDigitalCodeAsset(otherWarehouseList);
        respVO.setOtherWarehouseList(otherWarehouseList);

        // 出租信息
        List<AssetProductServiceRespVO> leaseWarehouseList = leaseWarehouseMapper.listUnitLeaseByUnitId(respVO.getUnitId());
        Map<String, String> priceUnitDict = DictCaChe.getByType(DictTypeConstants.COLDCHAIN_LEASE_PRICE_UNIT);
        leaseWarehouseList.forEach(leaseWarehouse -> {
            leaseWarehouse.setPriceUnit(priceUnitDict.get(leaseWarehouse.getPriceUnit()));
        });
        respVO.setWarehouseLeaseTotal(leaseWarehouseList.size());
        respVO.setLeaseServiceList(leaseWarehouseList);

        // 卖货信息
        List<AssetProductServiceRespVO> goodsList = leaseGoodsMapper.listUnitGoodsByUnitId(respVO.getUnitId());
        goodsList.forEach(leaseGoods -> {
            leaseGoods.setPriceUnit(priceUnitDict.get(leaseGoods.getPriceUnit()));
        });
        respVO.setGoodsTotal(goodsList.size());
        respVO.setGoodsServiceList(goodsList);

        // 冷库分级及认证信息
        String currentMonth = LocalDate.now().format(DateTimeFormatter.ofPattern("yyyy-MM"));
        AssetGradeMonthlyStatisticDO gradeDO = assetGradeMonthlyStatisticMapper.selectByMonthAndAssetId(currentMonth, respVO.getAssetId());
        if (ObjectUtil.isNotEmpty(gradeDO)) {
            respVO.setGrade(gradeDO.getGrade());
            respVO.setScore(gradeDO.getScore());
        } else {
            respVO.setGrade("未评级");
            respVO.setScore(null);
        }

        List<ValidationReportDO> validationReportDOList = validationReportMapper.listByAssetId(assetId);
        if (ObjectUtil.isNotEmpty(validationReportDOList)) {
            List<ValidationReportRespVO> validationReportList = CollectionUtils.convertList(validationReportDOList, report -> BeanUtils.toBean(report, ValidationReportRespVO.class));
            respVO.setValidationReport(validationReportList);
        } else {
            respVO.setValidationReport(Lists.newArrayList());
        }

        // 保存扫描日志
        saveScanLog(respVO);
        return respVO;
    }

    private void saveScanLog(AssetDigitalCodeDetailRespVO respVO) {
        AssetCodeScanLogDO codeScanLog = new AssetCodeScanLogDO();

        codeScanLog.setCode(respVO.getCode());
        codeScanLog.setUnitId(respVO.getUnitId());
        codeScanLog.setAssetId(respVO.getAssetId());
        codeScanLog.setLongitude(respVO.getLongitude());
        codeScanLog.setLatitude(respVO.getLatitude());

        codeScanLog.setUserId(SecurityUtils.getLoginUserId());

        assetCodeScanLogService
        assetCodeScanLogService.saveScanLog(respVO.getLatitude(), respVO.getLongitude());
    }


    /**
     * 加载一库一码
     *
     * @param otherWarehouseList
     */
    private void initAssetDigitalCodeAsset(List<AssetDigitalCodeAssetRespVO> otherWarehouseList) {

        List<Long> assetIdList = CollectionUtils.toList(otherWarehouseList, AssetDigitalCodeAssetRespVO::getId);

        // 拍照日志详情
        List<AssetCodeLogPhotoDetailsRespVO> allPhotoDetails = assetCodePhotoLogsMapper.listPhotoDetailsByAssetIds(assetIdList, AssetCodePhotoTypeConstants.TYPE_IMAGE);
        Map<Long, List<AssetCodeLogPhotoDetailsRespVO>> assetIdToPhone = CollectionUtils.groupingBy(allPhotoDetails, AssetCodeLogPhotoDetailsRespVO::getAssetId);

        // 资产属性集合
        Map<Long, Map<Long, Object>> assetIdToAttributes = attributesService.getByAssetIds(assetIdList);

        otherWarehouseList.forEach(otherWarehouse -> {
            List<AssetCodeLogPhotoDetailsRespVO> photoDetails = assetIdToPhone.get(otherWarehouse.getId());
            if (ObjectUtil.isNotEmpty(photoDetails)) {
                otherWarehouse.setPhotoDetails(photoDetails);
            }

            Map<Long, Object> attributesMap = assetIdToAttributes.get(otherWarehouse.getId());
            if (ObjectUtil.isEmpty(attributesMap)) {
                attributesMap = Maps.newHashMap();
            }
            otherWarehouse.setAttributesMap(attributesMap);
        });

    }

    /**
     * 加载一库一码
     *
     * @param asset
     */
    private void initAssetDigitalCodeAsset(AssetDigitalCodeAssetRespVO asset) {

        // 拍照日志详情
        List<AssetCodeLogPhotoDetailsRespVO> photoDetails = assetCodePhotoLogsMapper.listPhotoDetailsByLatest(asset.getId(), AssetCodePhotoTypeConstants.TYPE_IMAGE);
        asset.setPhotoDetails(photoDetails);

        // 资产属性集合
        Map<Long, Object> attributesMap = attributesService.getByAssetId(asset.getId());
        asset.setAttributesMap(attributesMap);

    }


    /**
     * 单位信息
     *
     * @param unitId
     * @return
     */
    private AssetDigitalCodeUnitRespVO getAssetDigitalCodeUnit(Long unitId) {
        UnitsDetailsRespVO units = unitsMapper.getUnits(unitId);
        // 校验存在
        if (units == null) {
            throw exception(UNITS_NOT_EXISTS);
        }

        AssetDigitalCodeUnitRespVO unitRespVO = new AssetDigitalCodeUnitRespVO();

        unitRespVO.setUnitId(units.getId());
        unitRespVO.setUnitName(units.getName());
        unitRespVO.setAreaId(units.getAreaId());
        unitRespVO.setAreaPath(units.getAreaPath());

        unitRespVO.setDescription(units.getDescription());
        unitRespVO.setAddress(units.getAddress());
        unitRespVO.setLatitude(units.getLatitude());
        unitRespVO.setLongitude(units.getLongitude());
        unitRespVO.setContact(units.getContact());
        unitRespVO.setMobile(units.getMobile());
        unitRespVO.setWebsite(units.getWebsite());


        List<UnitImagesDO> imagesList = unitImagesMapper.selectList(UnitImagesDO::getUnitId, unitId);
        List<UnitImagesRespVO> imageList = UnitImagesConvert.INSTANCE.convert(imagesList);
        unitRespVO.setImageList(imageList);

        return unitRespVO;
    }

    @Override
    public PageResult<AssetDigitalCodesRespVO> getAssetDigitalCodesPage(AssetDigitalCodesPageReqVO pageReqVO) {
        DataPermissionRespDTO dataPermission = DataPermissionCache.getDataPermissionByCode(DataPermissionCodeEnum.UNIT);

        IPage<AssetDigitalCodesRespVO> iPage = baseMapper.getAssetDigitalCodesPage(MyBatisUtils.buildPage(pageReqVO), pageReqVO, dataPermission);

        if (iPage.getRecords() != null) {
            iPage.getRecords().forEach(assetDigitalCodes -> {
                AreaRespDTO area = AreaCache.getById(assetDigitalCodes.getAreaId());
                if (area != null) {
                    assetDigitalCodes.setAreaNames(area.getNames());
                }
            });
        }

        return MyBatisUtils.buildPageResult(iPage);
    }

    /**
     * 获得空白一库一码分页
     *
     * @param pageReqVO
     * @return
     */
    @Override
    public PageResult<AssetDigitalEmptyCodesRespVO> getAssetDigitalEmptyCodesPage(AssetDigitalEmptyCodesPageReqVO pageReqVO) {

        Long tenantId = SecurityUtils.getTenantId();

        IPage<AssetDigitalEmptyCodesRespVO> iPage = baseMapper.getAssetDigitalEmptyCodesPage(MyBatisUtils.buildPage(pageReqVO), pageReqVO, tenantId);

        return MyBatisUtils.buildPageResult(iPage);
    }

    /**
     * 导出空白一库一码分页
     *
     * @param pageReqVO
     * @return
     */
    @Override
    public List<AssetDigitalEmptyCodesRespVO> exportEmptyCodes(AssetDigitalEmptyCodesPageReqVO pageReqVO) {
        Long tenantId = SecurityUtils.getTenantId();
        return baseMapper.exportEmptyCodes(pageReqVO, tenantId);
    }

    /**
     * 一库一码数量
     *
     * @param areaPath
     * @return
     */
    @Override
    public AssetCodeTotalStatisticsRespVO getAssetCodeTotal(String areaPath) {
        DataPermissionRespDTO dataPermission = DataPermissionCache.getDataPermissionByCode(DataPermissionCodeEnum.UNIT);

        return baseMapper.getAssetCodeTotal(areaPath, dataPermission);
    }

    /**
     * 一库一码统计分页，分开统计，手动分页
     *
     * @param pageReqVO
     * @return
     */
    @Override
    public PageResult<AssetDigitalCodesStatisticsRespVO> pageByStatistics(AssetDigitalCodesStatisticsPageReqVO pageReqVO) {
        DataPermissionRespDTO dataPermission = DataPermissionCache.getDataPermissionByCode(DataPermissionCodeEnum.UNIT);
        List<AssetDigitalCodesStatisticsRespVO> result = Lists.newArrayList();

        // 1. 统计地区经营主体总数和资产总数
        List<AssetDigitalCodesStatisticsRespVO> assetCountList = baseMapper.listByUnitAndAssetCount(pageReqVO, dataPermission);
        Map<Long, AssetDigitalCodesStatisticsRespVO> assetCountMap = CollectionUtils.convertMap(assetCountList, AssetDigitalCodesStatisticsRespVO::getAreaId);

        // 看下一级别，所以加1
        pageReqVO.setLevel(AreaUtils.calculateAreaPathLevel(pageReqVO.getAreaPath()) + 1);

        // 2. 经营主体张贴数
        List<AssetDigitalCodesStatisticsRespVO> postUnitCountList = baseMapper.listByUnitPostedCount(pageReqVO, dataPermission);
        Map<Long, AssetDigitalCodesStatisticsRespVO> postUnitCountMap = CollectionUtils.convertMap(postUnitCountList, AssetDigitalCodesStatisticsRespVO::getAreaId);

        // 3. 一库一码已张贴数量
        List<AssetDigitalCodesStatisticsRespVO> postCodeCountList = baseMapper.listByPostCodeCount(pageReqVO, dataPermission);
        Map<Long, AssetDigitalCodesStatisticsRespVO> postCodeCountMap = CollectionUtils.convertMap(postCodeCountList, AssetDigitalCodesStatisticsRespVO::getAreaId);

        // 综合三个查询的地区为循环基准
        Set<Long> areaIds = CollectionUtils.convertSet(assetCountList, AssetDigitalCodesStatisticsRespVO::getAreaId);

        areaIds.forEach(areaId -> {
            AssetDigitalCodesStatisticsRespVO statistics = new AssetDigitalCodesStatisticsRespVO();

            statistics.setAreaId(areaId);

            AreaRespDTO area = AreaCache.getById(areaId);
            if (area != null) {
                statistics.setAreaPathName(area.getPathName());
                statistics.setAreaNames(area.getNames());
            }

            AssetDigitalCodesStatisticsRespVO assetCount = assetCountMap.get(areaId);
            if (assetCount != null) {
                statistics.setUnitTotal(assetCount.getUnitTotal());
                statistics.setAssetTotal(assetCount.getAssetTotal());
            } else {
                statistics.setUnitTotal(0L);
                statistics.setAssetTotal(0L);
            }

            AssetDigitalCodesStatisticsRespVO postUnitCount = postUnitCountMap.get(areaId);
            if (postUnitCount != null) {
                statistics.setPostUnitTotal(postUnitCount.getPostUnitTotal());
            } else {
                statistics.setPostUnitTotal(0L);
            }

            AssetDigitalCodesStatisticsRespVO postCodeCount = postCodeCountMap.get(areaId);
            if (postCodeCount != null) {
                statistics.setPostCodeTotal(postCodeCount.getPostCodeTotal());
                statistics.setPostAssetTotal(postCodeCount.getPostAssetTotal());
            } else {
                statistics.setPostCodeTotal(0L);
                statistics.setPostAssetTotal(0L);
            }

            // TODO 注册逻辑未开发
            statistics.setRegisterUnitTotal(0L);
            statistics.setRegisterRate("00.0%");

            result.add(statistics);
        });

        result.forEach(statistics -> {
            statistics.setPostUnitRate(RateUtil.convertRate(statistics.getUnitTotal(), statistics.getPostUnitTotal()));
            statistics.setPostAssetRate(RateUtil.convertRate(statistics.getAssetTotal(), statistics.getPostAssetTotal()));
        });

        // 手动分页
        return customPaginate(pageReqVO.getPageNo(), pageReqVO.getPageSize(), result);
    }

    /**
     * 自定义分页
     *
     * @param pageNumber
     * @param pageSize
     * @param fullList
     * @param <T>
     * @return
     */
    private <T> PageResult<T> customPaginate(int pageNumber, int pageSize, List<T> fullList) {
        int startIndex = (pageNumber - 1) * pageSize;
        int endIndex = Math.min(startIndex + pageSize, fullList.size());

        List<T> pagedList = fullList.subList(startIndex, endIndex);

        PageResult<T> pageResult = new PageResult<>();
        pageResult.setList(pagedList);
        pageResult.setTotal((long) fullList.size());

        return pageResult;
    }

    /**
     * 导出一库一码统计信息 Excel
     *
     * @param pageReqVO
     * @return
     */
    @Override
    public List<AssetDigitalCodesStatisticsRespVO> listStatisticsExcel(AssetDigitalCodesStatisticsPageReqVO pageReqVO) {

        DataPermissionRespDTO dataPermission = DataPermissionCache.getDataPermissionByCode(DataPermissionCodeEnum.UNIT);

        List<AssetDigitalCodesStatisticsRespVO> baseInfo = baseMapper.exportAssetDigitalCodesStatistics(pageReqVO, dataPermission);

        baseInfo.forEach(statistics -> {
            statistics.setPostUnitRate(RateUtil.convertRate(statistics.getUnitTotal(), statistics.getPostUnitTotal()));
            statistics.setPostAssetRate(RateUtil.convertRate(statistics.getAssetTotal(), statistics.getPostAssetTotal()));

            AreaRespDTO area = AreaCache.getById(statistics.getAreaId());
            if (area != null) {
                statistics.setAreaPathName(area.getPathName());
                statistics.setAreaNames(area.getNames());
            }

            // TODO 注册逻辑未开发
            statistics.setRegisterUnitTotal(0L);
            statistics.setRegisterRate("00.0%");
        });
        return baseInfo;
    }

    /**
     * 根据资产编号查询
     *
     * @param assetIds
     * @return
     */
    @Override
    public List<AssetDigitalCodesDO> listByAssetIds(Set<Long> assetIds) {
        if (ObjectUtil.isEmpty(assetIds)) {
            return Lists.newArrayList();
        }
        return list(Wrappers.<AssetDigitalCodesDO>lambdaQuery().in(AssetDigitalCodesDO::getAssetId, assetIds));
    }

    /**
     * 批量创建一库一码
     *
     * @param assetIdList
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public Boolean saveBatchCode(List<Long> assetIdList) {
        String lockId = "LOCK_SAVE_BATCH_DIGITAL_CODE";
        try {
            boolean lock = RedisLockUtil.getLock(lockId, 3 * 1000);
            if (lock) {
                // 校验
                validateSaveDigitalCode(assetIdList);

                // 获取资产信息
                List<AssetDigitalCodesSaveReqVO> saveReqList = baseMapper.listByInitDigitalCode(assetIdList);
                if (ObjectUtil.isEmpty(saveReqList)) {
                    throw exception(ASSET_NOT_EMPTY);
                }
                List<AssetDigitalCodesDO> digitalCodesList = CollectionUtils.convertList(
                        saveReqList,
                        saveReq -> {
                            AssetDigitalCodesDO digitalCodes = AssetDigitalCodesConvert.INSTANCE.convert(saveReq);

                            String code = AssetDigitalCodeUtil.getCode(digitalCodes.getCodeArea(), digitalCodes.getCodeType());
                            if (StrUtil.isEmpty(code)) {
                                throw exception(ASSET_DIGITAL_CODES_GENERATE, saveReq.getUnitName(), saveReq.getAssetName());
                            }

                            Integer codeSerial = Integer.valueOf(code.substring(code.length() - 3));
                            digitalCodes.setCode(code);
                            digitalCodes.setCodeSerial(codeSerial);
                            digitalCodes.setBoundStatus(DefaultStatusEnum.YES.getStatus());

                            // 生成二维码
                            String qrUrl = generateQrCode(code, String.format(ASSET_DIGITAL_CODE_URL, code));
                            digitalCodes.setCodeImg(qrUrl);
                            return digitalCodes;
                        }
                );
                return super.saveBatch(digitalCodesList);
            }
        } catch (Exception ex) {
            AssetDigitalCodeUtil.clear();
            throw ex;
        } finally {
            RedisLockUtil.releaseLock(lockId);
        }
        throw exception(ASSET_DIGITAL_CODES_DUPLICATE_CLICK);
    }

    /**
     * 生成一库一码PDF
     *
     * @param assetIdList
     * @return
     */
    @Async
    @Override
    public void buildCodePdf(List<Long> assetIdList) {
        for (Long assetId : assetIdList) {
            buildCodePdf(assetId);
        }
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public String buildCodePdf(Long assetId) {
        return buildCodePdf(assetId, 1);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public String buildQuicklyPdf(Long assetId) {
        // 生成一库一码
        Boolean result = saveBatchCode(Lists.newArrayList(assetId));
        if (result) {
            return buildCodePdf(assetId);
        }
        throw exception("一库一码生成失败");
    }

    /**
     * 导出一库一码PDF
     *
     * @param assetIdList
     * @param response
     */
    @Override
    public void exportBatchCodePdf(List<Long> assetIdList, HttpServletResponse response) {
        List<String> pdfList = listPdfByAssetId(assetIdList);

        if (pdfList.size() == 1) {
            HttpUtils.download(pdfList.get(0), response);
        }
        // 导出
        ZipUtils.httpUrltoZip(pdfList, response, "一库一码" + DateUtils.format(LocalDateTime.now(), "yyyy-MM-dd_HHmmss"));
    }

    /**
     * 根据资产id下载一库一码
     *
     * @param assetId
     * @param response
     */
    @Override
    public void downloadCodePdfByAssetId(Long assetId, HttpServletResponse response) {
        AssetDigitalCodesDO digitalCodes = getByAssetId(assetId);
        if (digitalCodes != null) {
            String codePdf = digitalCodes.getCodePdf();
            if (StrUtil.isEmpty(codePdf)) {
                codePdf = buildCodePdf(assetId);
            }
            if (StrUtil.isEmpty(codePdf)) {
                throw exception("一库一码生成失败！");
            }
            HttpUtils.download(codePdf, response);
        }
        throw exception("请先生成一库一码信息");
    }

    /**
     * 根据资产绑定一库一码
     *
     * @param bindReq
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public Boolean bindCodeByAssetId(AppAssetDigitalCodesBindReqVO bindReq) {
        AssetDigitalCodesDO digitalCodes = baseMapper.selectByCode(bindReq.getCode());
        if (digitalCodes == null) {
            throw exception("输入内容有误，一库一码不存在！");
        }
        if (DefaultStatusEnum.isYes(digitalCodes.getBoundStatus())) {
            return true;
        }
        // 校验绑定编码
        validateBindCode(bindReq);

        // 单位
        UnitsDO units = unitsMapper.selectById(bindReq.getUnitId());
        if (units == null) {
            throw exception("经营主体不存在！");
        }

        // 资产
        AssetsDO assets = getAssetByIfAbsent(units, bindReq);

        // 一库一码
        digitalCodes.setUnitId(units.getId());
        digitalCodes.setAssetId(assets.getId());
        AreaRespDTO areaResp = AreaCache.getById(units.getAreaId());
        if (areaResp == null) {
            throw exception("经营主体所处区域不存在！");
        }
        digitalCodes.setAreaId(areaResp.getId());
        digitalCodes.setAreaName(areaResp.getPathName());
        digitalCodes.setCodeArea(String.valueOf(areaResp.getId()));
        digitalCodes.setBoundStatus(DefaultStatusEnum.YES.getStatus());

        // 保存
        saveOrUpdate(digitalCodes);

        //生成PDF
        TransactionSynchronizationManager.registerSynchronization(
                new TransactionSynchronizationAdapter() {
                    @Override
                    public void afterCommit() {
                        buildCodePdfByCode(digitalCodes.getCode());
                    }
                }
        );

        return true;
    }

    /**
     * 根据资产绑定一库一码
     *
     * @param units
     * @param bindReq
     * @return
     */
    private AssetsDO getAssetByIfAbsent(UnitsDO units, AppAssetDigitalCodesBindReqVO bindReq) {

        // 判断资产id是否为空
        if (bindReq.getAssetId() != null) {
            AssetsDO assets = assetsMapper.selectById(bindReq.getAssetId());
            if (assets == null) {
                throw exception("冷库不存在！");
            }
            return assets;
        }

        // 资产
        AssetsDO assets = new AssetsDO();
        assets.setUnitId(bindReq.getUnitId());
        assets.setName(bindReq.getAssetName());
        assets.setAssetType(bindReq.getAssetType());
        assets.setAssetModuleId(bindReq.getAssetModuleId());
        assets.setTenantId(units.getTenantId());
        assetsMapper.insert(assets);

        // 更新资产关联表
        assetRelApi.saveOrUpdate(AssetsConvert.INSTANCE.convertRelSaveDTO(assets));

        return assets;
    }

    /**
     * 校验绑定编码
     *
     * @param bindReq
     */
    private void validateBindCode(AppAssetDigitalCodesBindReqVO bindReq) {
        if (bindReq.getAssetId() == null && StrUtil.isEmpty(bindReq.getAssetName())) {
            throw exception("冷库名称和冷库请选择其中一个填写，不能同时为空！");
        }
    }

    /**
     * 根据资产id获取一库一码信息
     *
     * @param assetId
     * @return
     */
    private AssetDigitalCodesDO getByAssetId(Long assetId) {
        List<AssetDigitalCodesDO> list = list(Wrappers.<AssetDigitalCodesDO>lambdaQuery().eq(AssetDigitalCodesDO::getAssetId, assetId));
        if (ObjectUtil.isEmpty(list)) {
            return null;
        }
        return list.get(0);
    }

    /**
     * 根据资产id获取一库一码信息
     *
     * @param code
     * @return
     */
    private AssetDigitalCodesDO getByCode(String code) {
        List<AssetDigitalCodesDO> list = list(Wrappers.<AssetDigitalCodesDO>lambdaQuery().eq(AssetDigitalCodesDO::getCode, code));
        if (ObjectUtil.isEmpty(list)) {
            return null;
        }
        return list.get(0);
    }

    /**
     * 获取PDF列表
     *
     * @param assetIdList
     * @return
     */
    private List<String> listPdfByAssetId(List<Long> assetIdList) {
        List<AssetDigitalCodesDO> digitalCodesList = baseMapper.listByAssetId(assetIdList);
        if (ObjectUtil.isEmpty(digitalCodesList)) {
            throw exception("未查询到一库一码信息");
        }

        List<String> pdfList = CollectionUtils.convertList(digitalCodesList, AssetDigitalCodesDO::getCodePdf);
        if (ObjectUtil.isEmpty(pdfList) || assetIdList.size() != pdfList.size()) {
            throw exception("选中包含未生成PDF冷库，请先生成PDF");
        }
        // 保存打印日志
        assetCodePrintLogService.saveBatch(AssetCodePrintLogConvert.INSTANCE.convert(digitalCodesList));
        return pdfList;
    }

    /**
     * 获取PDF列表
     *
     * @param codes
     * @return
     */
    private List<String> listPdfByCodes(List<String> codes) {
        List<AssetDigitalCodesDO> digitalCodesList = baseMapper.listByCodes(codes);
        if (ObjectUtil.isEmpty(digitalCodesList)) {
            throw exception("未查询到一库一码信息");
        }
        List<String> pdfList = CollectionUtils.convertList(digitalCodesList, AssetDigitalCodesDO::getCodePdf);
        if (ObjectUtil.isEmpty(pdfList) || codes.size() != pdfList.size()) {
            throw exception("选中包含未生成PDF冷库，请先生成PDF");
        }
        return pdfList;
    }

    /**
     * 生成一库一码PDF
     *
     * @param assetId
     * @return
     */
    public String buildCodePdf(Long assetId, int count) {
        String lockId = "BUILD_CODE_PDF:" + assetId;
        try {
            boolean lock = RedisLockUtil.getLock(lockId, 3 * 1000);
            if (lock) {
                logger.info("已-----获取到锁:" + lockId + "  count：" + count);
                AssetDigitalCodePdfBO digitalCodePdf = convertAssetDigitalCodePdf(assetId);
                if (StrUtil.isEmpty(digitalCodePdf.getCodeImage()) || StrUtil.isEmpty(digitalCodePdf.getTemplateUrl()) || StrUtil.isNotEmpty(digitalCodePdf.getCodePdf())) {
                    return "";
                }
                // 生成PDF
                Long start = System.currentTimeMillis();
                logger.info("生成PDF开始时间:" + start);
                String codePdf = createPdf(digitalCodePdf);
                Long end = System.currentTimeMillis();
                logger.info("生成PDF结束时间:" + end);
                logger.info("生成PDF用时---------------------:" + (end - start));
                if (StrUtil.isNotEmpty(codePdf)) {
                    // 修改PDF路径
                    baseMapper.updateCodePdfById(digitalCodePdf.getId(), codePdf);

                    RedisLockUtil.releaseLock(lockId);
                    return codePdf;
                }
                return "";
            }
            logger.info("未----获取到锁:" + lockId + "  count：" + count);
            if (count > 2) {
                return "";
            }
            return buildCodePdf(assetId, ++count);
        } catch (Exception ex) {
            ex.printStackTrace();
        } finally {
            RedisLockUtil.releaseLock(lockId);
        }
        return "";
    }

    private String createPdf(AssetDigitalCodePdfBO digitalCodePdf) {
        // 模版数据
        Map<String, Map<EPdfProperty, PdfPropertyDTO>> dataPropertyTable = TablePdfPropertyUtil.convertObject(digitalCodePdf);

        log.info("[createPdf] 模版数据 : {}", dataPropertyTable);

        // 构建PDF
        InputStream pdf = TemplatePdfUtil.getPdf(digitalCodePdf.getTemplateUrl(), dataPropertyTable);
        // 上传
        return fileApi.createFile(digitalCodePdf.getCode() + ".pdf", ASSET_DIGITAL_CODE_PDF_FILE_MODEL, IoUtil.readBytes(pdf));
    }

    /**
     * 根据资产id查询资产详情
     *
     * @param assetId
     * @return
     */
    private AssetDigitalCodePdfBO convertAssetDigitalCodePdf(Long assetId) {
        AssetDigitalCodePdfBO codePdf = baseMapper.getDigitalCodePdfByAssetId(assetId);

        String assetInfo = String.format("编码：%s\n所属单位：%s\n地址：%s", codePdf.getCode(), codePdf.getUnitName(), codePdf.getAddress());
        codePdf.setAssetInfo(assetInfo);

        AssetsDigitalCodeConfigRespDTO digitalCodeConfig = AssetDigitalCodeConfigCache.getByAreaId(codePdf.getAreaId());
        if (digitalCodeConfig == null) {
            throw exception(String.format("一库一码未配置【%s】", codePdf.getAreaId()));
        }
        codePdf.setCopyrightText(digitalCodeConfig.getCopyrightText());
        codePdf.setCopyrightImage(digitalCodeConfig.getCopyrightImage());
        codePdf.setTemplateUrl(digitalCodeConfig.getTemplateUrl());
        return codePdf;
    }

    /**
     * 生成一库一码二维码
     *
     * @param code 一库一码
     * @return 二维码图片URL
     * @description 宽高：205*205，文件上传到OSS，名称为一库一码的code，后缀为png
     */
    private String generateQrCode(String code, String url) {
        BufferedImage bufferedImage = QrCodeUtil.generate(url, 205, 205);

        // 文件类型
        String fileType = "png";
        byte[] content = getImageBytes(bufferedImage, fileType);

        return fileApi.createFile(code + "." + fileType, ASSET_DIGITAL_CODE_PDF_FILE_MODEL, content);
    }

    /**
     * 获取图片字节数组
     *
     * @param bufferedImage 图片
     * @param formatName    格式
     * @return 字节数组
     */
    private byte[] getImageBytes(BufferedImage bufferedImage, String formatName) {
        ByteArrayOutputStream os = new ByteArrayOutputStream();
        try {
            ImageIO.write(bufferedImage, formatName, os);
            return os.toByteArray();
        } catch (Exception e) {
            throw new RuntimeException("Failed to convert BufferedImage to InputStream", e);
        } finally {
            IoUtil.close(os);
        }
    }


    /**
     * 资产一库一码生成校验
     *
     * @param assetIdList
     */
    private void validateSaveDigitalCode(List<Long> assetIdList) {
        if (ObjectUtil.isEmpty(assetIdList)) {
            throw exception("请选择冷库");
        }
        // 校验资产是否重复生成
        List<String> assetNames = baseMapper.countRepetitionGenerateCode(assetIdList);
        if (ObjectUtil.isNotEmpty(assetNames)) {
            throw exception(ASSET_DIGITAL_CODES_DUPLICATE, StringUtil.join(assetNames, "、"));
        }
        // 经营主体未设置区域
        List<String> unitNames = baseMapper.countNotAreaUnit(assetIdList);
        if (ObjectUtil.isNotEmpty(unitNames)) {
            throw exception(UNIT_AREA_NULL, StringUtil.join(unitNames, "、"));
        }
    }

    /**
     * 通过一库一码集合查找一库一码信息
     *
     * @param assetCodes
     */
    @Override
    public List<AssetDigitalCodesDO> listByAssetCodes(List<String> assetCodes) {
        return baseMapper.listByAssetCodes(assetCodes);
    }

    /**
     * 生成空白一库一码
     *
     * @param pageReqVO
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public Boolean generateEmptyCodes(AssetGenerateEmptyCodeReqVO pageReqVO) {
        try {
            if (pageReqVO.getCount() == null || pageReqVO.getCount() <= 0) {
                throw exception(ASSET_DIGITAL_CODES_GENERATE_COUNT_ERROR);
            }
            if (pageReqVO.getAreaCode() == null || pageReqVO.getAreaCode().length() != 6) {
                throw exception(ASSET_DIGITAL_CODES_AREA_CODE_ERROR);
            }
            if (pageReqVO.getCount() > 10000) { // 限制单次生成数量
                throw exception(ASSET_DIGITAL_CODES_GENERATE_COUNT_MAX);
            }

            // 异步生成文本码
            CompletableFuture<List<AssetGenerateCodeItemRespVO>> future = generateCodes(pageReqVO);
            List<AssetGenerateCodeItemRespVO> items = future.get(); // 等待文本码生成完成

            if (CollectionUtils.isEmpty(items)) {
                throw exception(ASSET_DIGITAL_CODES_GENERATE_ERROR, "未生成任何一库一码");
            }

            // 并发生成二维码
            ExecutorService executor = Executors.newFixedThreadPool(10); // 自定义线程池
            List<CompletableFuture<AssetDigitalCodesDO>> futureList = new ArrayList<>();

            for (AssetGenerateCodeItemRespVO item : items) {
                CompletableFuture<AssetDigitalCodesDO> qrFuture = CompletableFuture.supplyAsync(() -> {
                    AssetDigitalCodesDO codeDO = new AssetDigitalCodesDO();
                    codeDO.setCode(item.getCode());
                    codeDO.setCodeSerial(item.getCodeSerial());
                    codeDO.setCodeArea(item.getCodeArea());
                    codeDO.setAreaId(item.getAreaId());
                    codeDO.setAreaName(item.getAreaName());
                    codeDO.setTenantId(SecurityUtils.getTenantId());

                    // 生成二维码图片链接
                    String qrUrl = generateQrCode(item.getCode(), String.format(ASSET_DIGITAL_CODE_URL, item.getCode()));
                    codeDO.setCodeImg(qrUrl);

                    return codeDO;
                }, executor);

                futureList.add(qrFuture);
            }

            // 等所有二维码生成任务完成
            List<AssetDigitalCodesDO> codeList = futureList.stream()
                    .map(CompletableFuture::join) // 注意join而不是get，join不会抛检查异常
                    .collect(Collectors.toList());

            // 批量保存到数据库
            final int batchSize = 500;
            for (int i = 0; i < codeList.size(); i += batchSize) {
                int end = Math.min(i + batchSize, codeList.size());
                saveBatch(codeList.subList(i, end));
            }

            executor.shutdown(); // 记得关线程池
        } catch (Exception e) {
            log.error("[generateEmptyCodes] 生成空白一库一码失败，异常信息：{}", e.getMessage(), e);
            throw exception(ASSET_DIGITAL_CODES_GENERATE_ERROR, e.getMessage());
        }

        return true;
    }


    /**
     * 批量生成一库一码（省市区编码 + 年月 + 流水码，固定14位，使用Redis防止重复）
     * eg：360404 2504 0001
     *
     * @param pageReqVO req
     * @return List<AssetGenerateCodeItemRespVO> 编码对象集合
     */
    @Async
    public CompletableFuture<List<AssetGenerateCodeItemRespVO>> generateCodes(AssetGenerateEmptyCodeReqVO pageReqVO) {

        int count = pageReqVO.getCount();
        String areaCode = pageReqVO.getAreaCode();

        if (count <= 0) {
            throw exception(ASSET_DIGITAL_CODES_GENERATE_COUNT_ERROR);
        }
        if (areaCode == null || areaCode.length() != 6) {
            throw exception(ASSET_DIGITAL_CODES_AREA_CODE_ERROR);
        }

        List<AssetGenerateCodeItemRespVO> result = Lists.newArrayList();

        String areaPathName = "";
        if (ObjectUtil.isNotEmpty(pageReqVO.getAreaPath())) {
            AreaRespDTO areaRespDTO = AreaCache.getByPath(pageReqVO.getAreaPath());
            if (areaRespDTO != null) {
                areaPathName = areaRespDTO.getPathName();
            }
        }


        // 默认当前年月（yyMM），超限自动往后跳月
        String yearMonth = resolveAvailableYearMonth(areaCode, count);

        String fixedPart = areaCode + yearMonth; // 10位固定部分

        String redisKey = ASSET_DIGITAL_CODE_SERIAL_REDIS_KEY + fixedPart;

        log.info("[generateCodes] 开始生成一库一码：count = {}, areaCode = {}, redisKey = {}", count, areaCode, redisKey);

        try {
            // 关键：Redis里直接INCRBY，拿到这次要用的最后一个号
            long endSerial = RedisCacheUtil.incrBy(redisKey, count);
            long startSerial = endSerial - count + 1;

            log.info("[generateCodes] Redis分配的流水号：{} ~ {}", startSerial, endSerial);

            for (long serial = startSerial; serial <= endSerial; serial++) {
                String serialPart = String.format("%04d", serial); // 固定4位，前面补0
                String code = fixedPart + serialPart;

                if (code.length() != 14) {
                    log.error("[generateCodes] 长度错误！生成的 code = {}, 实际长度 = {}", code, code.length());
                    throw exception(ASSET_DIGITAL_CODES_LENGTH_ERROR);
                }

                AssetGenerateCodeItemRespVO codeItem = new AssetGenerateCodeItemRespVO();
                codeItem.setCode(code);
                codeItem.setCodeSerial(Integer.parseInt(serialPart));
                codeItem.setCodeArea(areaCode);
                codeItem.setAreaId(Long.valueOf(areaCode));
                codeItem.setAreaName(areaPathName);

                result.add(codeItem);
            }

            log.info("[generateCodes] 成功生成 {} 个一库一码", count);
            return CompletableFuture.completedFuture(result);

        } catch (Exception ex) {
            log.error("[generateCodes] 生成一库一码出错！异常信息：{}", ex.getMessage(), ex);
            throw exception(ASSET_DIGITAL_CODES_GENERATE_ERROR, ex.getMessage());
        }
    }

    /**
     * 判断可用年月（yyMM）是否已满9999，如果满了则向后推一个月
     *
     * @param areaCode 地区码
     * @param count    本次要申请的数量
     * @return 可用年月（yyMM）
     */
    public static String resolveAvailableYearMonth(String areaCode, int count) {
        String yearMonth = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyMM"));

        for (int i = 0; i < MAX_MONTH_LOOKAHEAD; i++) {
            String redisKey = ASSET_DIGITAL_CODE_SERIAL_REDIS_KEY + areaCode + yearMonth;
            Long currentSerial = RedisCacheUtil.getLong(redisKey);

            if (currentSerial == null || currentSerial + count <= MAX_SERIAL) {
                return yearMonth;
            }

            // 超出，向后推一个月
            yearMonth = LocalDate.parse("20" + yearMonth + "01", DateTimeFormatter.ofPattern("yyyyMMdd"))
                    .plusMonths(1)
                    .format(DateTimeFormatter.ofPattern("yyMM"));
        }

        throw new RuntimeException("连续12个月编码已满，无法分配一库一码");
    }


    /**
     * 生成未绑定的一库一码PDF
     *
     * @param codeList
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    @Async
    @Override
    public void buildEmptyCodePdf(List<String> codeList) {
        for (String code : codeList) {
            buildCodePdfByCode(code);
        }
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public String buildCodePdfByCode(String code) {
        return buildCodePdfByCode(code, 1);
    }

    /**
     * 生成一库一码PDF - 通过code
     *
     * @param code
     * @return
     */
    public String buildCodePdfByCode(String code, int count) {
        String lockId = "BUILD_EMPTY_CODE_PDF_BY_CODE:" + code;
        try {
            boolean lock = RedisLockUtil.getLock(lockId, 3 * 1000);
            if (lock) {
                logger.info("已-----获取到锁:" + lockId + "  count：" + count);
                AssetDigitalCodePdfBO digitalCodePdf = convertAssetDigitalCodePdfByCode(code);
                if (StrUtil.isEmpty(digitalCodePdf.getCodeImage()) || StrUtil.isEmpty(digitalCodePdf.getTemplateUrl()) || StrUtil.isNotEmpty(digitalCodePdf.getCodePdf())) {
                    return "";
                }
                // 生成PDF
                Long start = System.currentTimeMillis();
                logger.info("生成PDF开始时间:" + start);
                String codePdf = createPdf(digitalCodePdf);
                Long end = System.currentTimeMillis();
                logger.info("生成PDF结束时间:" + end);
                logger.info("生成PDF用时---------------------:" + (end - start));
                if (StrUtil.isNotEmpty(codePdf)) {
                    // 修改PDF路径
                    baseMapper.updateCodePdfById(digitalCodePdf.getId(), codePdf);

                    RedisLockUtil.releaseLock(lockId);
                    return codePdf;
                }
                return "";
            }
            logger.info("未----获取到锁:" + lockId + "  count：" + count);
            if (count > 2) {
                return "";
            }
            return buildCodePdfByCode(code, ++count);
        } catch (Exception ex) {
            ex.printStackTrace();
        } finally {
            RedisLockUtil.releaseLock(lockId);
        }
        return "";
    }

    /**
     * 根据资产id查询资产详情
     *
     * @param code
     * @return
     */
    private AssetDigitalCodePdfBO convertAssetDigitalCodePdfByCode(String code) {

        AssetDigitalCodesDO assetDigitalCodesDO = baseMapper.selectOne(Wrappers.<AssetDigitalCodesDO>lambdaQuery()
                .eq(AssetDigitalCodesDO::getCode, code)
                .eq(AssetDigitalCodesDO::getStatus, 1));

        AssetDigitalCodePdfBO codePdf = new AssetDigitalCodePdfBO();
        codePdf.setId(assetDigitalCodesDO.getId());
        codePdf.setCode(code);
        codePdf.setCodeWhite(code);
        codePdf.setUnitName("");
        codePdf.setAreaId(assetDigitalCodesDO.getAreaId());
        codePdf.setAddress("");
        codePdf.setCodePdf(assetDigitalCodesDO.getCodePdf());
        codePdf.setTenantId(null);
        codePdf.setCodeImage(assetDigitalCodesDO.getCodeImg());


        String assetInfo = String.format("编码：%s\n所属单位：%s\n地址：%s", codePdf.getCode(), codePdf.getUnitName(), codePdf.getAddress());
        codePdf.setAssetInfo(assetInfo);

        // areaId 取省，即前两位数字
        long areaId = AreaConstants.ID_ROOT;
        if (ObjectUtil.isNotEmpty(codePdf.getAreaId())) {
            areaId = Long.parseLong(codePdf.getCode().substring(0, 2) + "0000");
        }

        AssetsDigitalCodeConfigRespDTO digitalCodeConfig = AssetDigitalCodeConfigCache.getByAreaId(areaId);
        if (digitalCodeConfig == null) {
            throw exception(String.format("一库一码未配置【%s】", codePdf.getAreaId()));
        }
        codePdf.setCopyrightText(digitalCodeConfig.getCopyrightText());
        codePdf.setCopyrightTextWrite(digitalCodeConfig.getCopyrightText());
        codePdf.setCopyrightImage(digitalCodeConfig.getCopyrightImage());
        codePdf.setTemplateUrl(digitalCodeConfig.getTemplateUrl());

        log.info("[convertAssetDigitalCodePdfByCode] 配置信息：{}", codePdf);

        return codePdf;
    }

    /**
     * 根据code下载一库一码
     *
     * @param code
     * @param response
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public void downloadCodePdfByCode(String code, HttpServletResponse response) {
        AssetDigitalCodesDO digitalCodes = getByCode(code);

        if (digitalCodes != null) {
            String codePdf = digitalCodes.getCodePdf();
            if (StrUtil.isEmpty(codePdf)) {
                codePdf = buildCodePdfByCode(code);
            }
            if (StrUtil.isEmpty(codePdf)) {
                throw exception("一库一码生成失败！");
            }
            HttpUtils.download(codePdf, response);
        }
        throw exception("请先生成一库一码信息");
    }

    /**
     * 导出未绑定一库一码PDF
     *
     * @param codes
     * @param response
     */
    @Override
    public void exportBatchEmptyCodePdf(List<String> codes, HttpServletResponse response) {
        List<String> pdfList = listPdfByCodes(codes);
        if (pdfList.size() == 1) {
            HttpUtils.download(pdfList.get(0), response);
        }
        // 导出
        ZipUtils.httpUrltoZip(pdfList, response, "一库一码" + DateUtils.format(LocalDateTime.now(), "yyyy-MM-dd_HHmmss"));
    }

    /**
     * 校验一库一码绑定
     *
     * @param code
     * @return
     */
    @Override
    public Boolean validateBindCode(String code) {
        AssetDigitalCodesDO digitalCodes = baseMapper.selectByCode(code);
        if (digitalCodes == null) {
            throw exception("输入内容有误，一库一码不存在！");
        }
        return DefaultStatusEnum.isYes(digitalCodes.getBoundStatus());
    }

    /**
     * 根据经营主体id查询一库一码信息
     *
     * @param unitId
     * @param unitName
     * @return
     */
    @Override
    public PageResult<AssetDigitalCodesRespVO> pageByUnitId(Long unitId, String unitName) {
        AssetDigitalCodesPageReqVO pageReq = new AssetDigitalCodesPageReqVO();

        pageReq.setUnitId(unitId);
        pageReq.setUnitName(unitName);

        return getAssetDigitalCodesPage(pageReq);
    }

    @Override
    public List<AssetsDigitalCodeRespDTO> listAssetDigitalCode(LocalDateTime startTime, LocalDateTime endTime) {
        return BeanUtils.toBean(baseMapper.listAssetDigitalCode(startTime, endTime), AssetsDigitalCodeRespDTO.class);
    }

}
