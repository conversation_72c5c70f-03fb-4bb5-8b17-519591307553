package com.lw.apcc.module.coldchain.controller.admin.assets;

import org.springframework.web.bind.annotation.*;
import org.springframework.validation.annotation.Validated;
import org.springframework.security.access.prepost.PreAuthorize;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Operation;

import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;

import java.util.*;
import java.io.IOException;

import com.lw.apcc.common.pojo.PageParam;
import com.lw.apcc.common.pojo.PageResult;
import com.lw.apcc.common.pojo.CommonResult;
import com.lw.apcc.common.util.object.BeanUtils;
import static com.lw.apcc.common.pojo.CommonResult.success;

import com.lw.apcc.framework.excel.core.util.ExcelUtils;

import com.lw.apcc.framework.operatelog.core.annotations.OperateLog;
import static com.lw.apcc.framework.operatelog.core.enums.OperateTypeEnum.*;

import com.lw.apcc.module.coldchain.controller.admin.assets.vo.*;
import com.lw.apcc.module.coldchain.dal.dataobject.assets.AssetCodeScanLogDO;
import com.lw.apcc.module.coldchain.service.assets.AssetCodeScanLogService;

@Tag(name = "管理后台 - 一库一码扫码记录信息")
@RestController
@RequestMapping("/coldchain/asset-code-scan-log")
@Validated
public class AssetCodeScanLogController {

    @Resource
    private AssetCodeScanLogService assetCodeScanLogService;

    @PostMapping("/create")
    @Operation(summary = "创建一库一码扫码记录信息")
    @PreAuthorize("@ss.hasPermission('coldchain:asset-code-scan-log:create')")
    public CommonResult<Long> createAssetCodeScanLog(@Valid @RequestBody AssetCodeScanLogSaveReqVO createReqVO) {
        return success(assetCodeScanLogService.createAssetCodeScanLog(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新一库一码扫码记录信息")
    @PreAuthorize("@ss.hasPermission('coldchain:asset-code-scan-log:update')")
    public CommonResult<Boolean> updateAssetCodeScanLog(@Valid @RequestBody AssetCodeScanLogSaveReqVO updateReqVO) {
        assetCodeScanLogService.updateAssetCodeScanLog(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除一库一码扫码记录信息")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('coldchain:asset-code-scan-log:delete')")
    public CommonResult<Boolean> deleteAssetCodeScanLog(@RequestParam("id") Long id) {
        assetCodeScanLogService.deleteAssetCodeScanLog(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得一库一码扫码记录信息")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    public CommonResult<AssetCodeScanLogRespVO> getAssetCodeScanLog(@RequestParam("id") Long id) {
        AssetCodeScanLogDO assetCodeScanLog = assetCodeScanLogService.getAssetCodeScanLog(id);
        return success(BeanUtils.toBean(assetCodeScanLog, AssetCodeScanLogRespVO.class));
    }

    @GetMapping("/page")
    @Operation(summary = "获得一库一码扫码记录信息分页")
    public CommonResult<PageResult<AssetCodeScanLogRespVO>> getAssetCodeScanLogPage(@Valid AssetCodeScanLogPageReqVO pageReqVO) {
        PageResult<AssetCodeScanLogDO> pageResult = assetCodeScanLogService.getAssetCodeScanLogPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, AssetCodeScanLogRespVO.class));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出一库一码扫码记录信息 Excel")
    @PreAuthorize("@ss.hasPermission('coldchain:asset-code-scan-log:export')")
    @OperateLog(type = EXPORT)
    public void exportAssetCodeScanLogExcel(@Valid AssetCodeScanLogPageReqVO pageReqVO, HttpServletResponse response) throws IOException {
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        List<AssetCodeScanLogDO> list = assetCodeScanLogService.getAssetCodeScanLogPage(pageReqVO).getList();
        // 导出 Excel
        ExcelUtils.write(response, "一库一码扫码记录信息.xls", "数据", AssetCodeScanLogRespVO.class, BeanUtils.toBean(list, AssetCodeScanLogRespVO.class));
    }

}
