package com.lw.apcc.module.coldchain.controller.admin.assets.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

import java.time.LocalDateTime;
import com.alibaba.excel.annotation.*;

@Schema(description = "管理后台 - 一库一码扫码记录信息 Response VO")
@Data
@ExcelIgnoreUnannotated
public class AssetCodeScanLogRespVO {

    @Schema(description = "主键ID;", requiredMode = Schema.RequiredMode.REQUIRED, example = "9042")
    @ExcelIgnore
    private Long id;

    @Schema(description = "code码;", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("一库一码")
    private Long code;

    @Schema(description = "用户ID;", requiredMode = Schema.RequiredMode.REQUIRED, example = "3914")
    @ExcelIgnore
    private Long userId;

    @Schema(description = "用户名称", example = "12649")
    @ExcelProperty("用户名称")
    private String userName;

    @Schema(description = "单位ID", example = "12649")
    @ExcelIgnore
    private Long unitId;

    @Schema(description = "单位ID", example = "12649")
    @ExcelProperty("单位名称")
    private String unitName;

    @Schema(description = "资产ID;", example = "5171")
    @ExcelIgnore
    private Long assetId;

    @Schema(description = "资产ID;", example = "5171")
    @ExcelProperty("资产名称")
    private String assetName;

    @Schema(description = "区域经度;")
    @ExcelProperty("区域经度")
    private String longitude;

    @Schema(description = "区域纬度")
    @ExcelProperty("区域纬度")
    private String latitude;

    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("扫码时间")
    private LocalDateTime createTime;

}
