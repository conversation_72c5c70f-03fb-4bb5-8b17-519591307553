package com.lw.apcc.module.coldchain.controller.app.assets;

import com.lw.apcc.common.pojo.CommonResult;
import com.lw.apcc.common.pojo.PageResult;
import com.lw.apcc.module.coldchain.controller.admin.assets.vo.AssetsRespVO;
import com.lw.apcc.module.coldchain.controller.admin.assets.vo.AssetsSaveReqVO;
import com.lw.apcc.module.coldchain.controller.admin.assets.vo.AssetsSimpleReqVO;
import com.lw.apcc.module.coldchain.controller.admin.assets.vo.AssetsSimpleRespVO;
import com.lw.apcc.module.coldchain.controller.app.assets.vo.*;
import com.lw.apcc.module.coldchain.service.assets.AppAssetsService;
import com.lw.apcc.module.coldchain.service.assets.AssetsService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

import static com.lw.apcc.common.pojo.CommonResult.success;


@Tag(name = "App 后台 - 资产信息")
@RestController
@RequestMapping("/coldchain/assets")
@Validated
public class AppAssetsController {

    @Resource
    private AppAssetsService appAssetsService;

    @Resource
    private AssetsService assetsService;

    @GetMapping("/list-by-default-unit")
    @Operation(summary = "根据默认经营主体查询资产信息")
    public CommonResult<List<AppAssetsRespVO>> listByDefaultUnit(@RequestParam("assetType") String assetType, @RequestParam(value = "unitId", required = false) Long unitId) {
        return success(appAssetsService.listByDefaultUnit(assetType, unitId));
    }

    @GetMapping("/list-asset-total-statistics")
    @Operation(summary = "按照资产模型统计资产数量")
    public CommonResult<List<AppAssetTotalStatisticsRespVO>> listAssetTotalStatistics(@RequestParam("assetType") String assetType) {
        return success(appAssetsService.listAssetTotalStatistics(assetType));
    }

    @GetMapping("/page")
    @Operation(summary = "获得资产信息分页")
    public CommonResult<PageResult<AppAssetsPageRespVO>> pageByAssets(@Valid AppAssetsPageReqVO pageReqVO) {
        return success(appAssetsService.pageByAssets(pageReqVO));
    }

    @GetMapping("/list-dryer-by-current-login-user")
    @Operation(summary = "查询当前登录用户烘干设备列表")
    public CommonResult<PageResult<AppAssetsPageRespVO>> listDryerByCurrentLoginUser(@RequestParam(value = "name", required = false) String name, @RequestParam(value = "unitId", required = false) Long unitId) {
        return success(appAssetsService.listDryerByCurrentLoginUser(name, unitId));
    }

    @GetMapping("/list-by-current-login-user")
    @Operation(summary = "查询当前登录用户冷藏车列表")
    public CommonResult<PageResult<AppAssetsPageRespVO>> listByCurrentLoginUser(@RequestParam(value = "name", required = false) String name, @RequestParam(value = "unitId", required = false) Long unitId) {
        return success(appAssetsService.listByCurrentLoginUser(name, unitId));
    }

    @GetMapping("/get")
    @Operation(summary = "获得资产清单")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    public CommonResult<AssetsRespVO> getAssets(@RequestParam("id") Long id) {
        return success(appAssetsService.getAssets(id));
    }

    @PostMapping("/create")
    @Operation(summary = "创建资产信息")
    public CommonResult<Long> createAssets(@Valid @RequestBody AssetsSaveReqVO createReqVO) {
        return success(appAssetsService.createAssets(createReqVO));
    }

    @PostMapping("/update")
    @Operation(summary = "更新资产信息")
    public CommonResult<Boolean> updateAssets(@RequestBody AssetsSaveReqVO updateReqVO) {
        return success(appAssetsService.updateAssets(updateReqVO));
    }

    @GetMapping("/get-asset-statistics-by-user")
    @Operation(summary = "我的冷库统计信息")
    public CommonResult<AppAssetStatisticsUserRespVO> getAssetStatisticsByUser(@RequestParam(value = "unitId", required = false) Long unitId) {
        return success(appAssetsService.getAssetStatisticsByUser(unitId));
    }

    @GetMapping("/page-asset-by-user")
    @Operation(summary = "分页我的冷库统计信息")
    public CommonResult<PageResult<AppAssetUserPageRespVO>> pageAssetByUser(AppAssetUserPageReqVO pageReqVO) {
        return success(appAssetsService.pageAssetByUser(pageReqVO));
    }

    @GetMapping("/get-asset-details-by-id")
    @Operation(summary = "根据id查询资产详情")
    public CommonResult<AppAssetDetailsRespVO> getAssetDetailsById(@RequestParam("id") Long id) {
        return success(appAssetsService.getAssetDetailsById(id));
    }

    @GetMapping("/list-simple-unit-id")
    @Operation(summary = "根据经营主体ID查询资产精简信息")
    public CommonResult<List<AssetsSimpleRespVO>> listSimpleByUnitId(AssetsSimpleReqVO reqVO) {
        return success(appAssetsService.listSimpleByUnitId(reqVO));
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除资产信息")
    @Parameter(name = "id", description = "编号", required = true)
    public CommonResult<Boolean> deleteAssets(@RequestParam("id") Long id) {
        assetsService.deleteAssets(id);
        return success(true);
    }

    /**
     * @description 根据id修改名称
     * <AUTHOR> yang
     * @date 2025/4/13 15:09
     */
    @GetMapping("/update-name-by-id")
    @Operation(summary = "根据id修改名称")
    public CommonResult<Boolean> updateNameById(@RequestParam(value = "id") Long id, @RequestParam("name") String name) {
        return success(assetsService.updateNameById(id, name));
    }

    /**
     * @description 根据id修改定位
     * <AUTHOR> yang
     * @date 2025/4/13 15:09
     */
    @PostMapping("/update-positioning-by-id")
    @Operation(summary = "根据id修改定位")
    public CommonResult<Boolean> updatePositioningById(@RequestBody AppAssetPositioningUpdateReqVO positioningUpdate) {
        return success(assetsService.updatePositioningById(positioningUpdate));
    }

    /**
     * @description 根据id修改定位
     * <AUTHOR> yang
     * @date 2025/4/13 15:09
     */
    @GetMapping("/list-not-bind-code-by-unit-id")
    @Operation(summary = "根据经营主体Id查询没有绑定的冷库")
    public CommonResult<List<AppAssetNotBindCodeRespVO>> listNotBindCodeByUnitId(@RequestParam("unitId") Long unitId, @RequestParam(value = "name", required = false) String name) {
        return success(assetsService.listNotBindCodeByUnitId(unitId, name));
    }

}
