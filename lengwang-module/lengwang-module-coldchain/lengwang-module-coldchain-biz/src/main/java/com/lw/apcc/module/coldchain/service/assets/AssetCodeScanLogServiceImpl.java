package com.lw.apcc.module.coldchain.service.assets;

import com.lw.apcc.common.pojo.PageResult;
import com.lw.apcc.common.util.object.BeanUtils;
import com.lw.apcc.framework.mybatis.core.service.BaseServiceImpl;
import com.lw.apcc.module.coldchain.controller.admin.assets.vo.AssetCodeScanLogPageReqVO;
import com.lw.apcc.module.coldchain.controller.admin.assets.vo.AssetCodeScanLogRespVO;
import com.lw.apcc.module.coldchain.controller.admin.assets.vo.AssetCodeScanLogSaveReqVO;
import com.lw.apcc.module.coldchain.dal.dataobject.assets.AssetCodeScanLogDO;
import com.lw.apcc.module.coldchain.dal.mapper.assets.AssetCodeScanLogMapper;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

/**
 * 一库一码扫码记录信息 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class AssetCodeScanLogServiceImpl extends BaseServiceImpl<AssetCodeScanLogMapper, AssetCodeScanLogDO> implements AssetCodeScanLogService {


    @Override
    public Long createAssetCodeScanLog(AssetCodeScanLogSaveReqVO createReqVO) {
        // 插入
        AssetCodeScanLogDO assetCodeScanLog = BeanUtils.toBean(createReqVO, AssetCodeScanLogDO.class);
        baseMapper.insert(assetCodeScanLog);
        // 返回
        return assetCodeScanLog.getId();
    }

    @Override
    public void updateAssetCodeScanLog(AssetCodeScanLogSaveReqVO updateReqVO) {
        // 更新
        AssetCodeScanLogDO updateObj = BeanUtils.toBean(updateReqVO, AssetCodeScanLogDO.class);
        baseMapper.updateById(updateObj);
    }

    @Override
    public void deleteAssetCodeScanLog(Long id) {
        // 删除
        baseMapper.deleteById(id);
    }

    @Override
    public AssetCodeScanLogDO getAssetCodeScanLog(Long id) {
        return baseMapper.selectById(id);
    }

    @Override
    public PageResult<AssetCodeScanLogRespVO> getAssetCodeScanLogPage(AssetCodeScanLogPageReqVO pageReqVO) {
        return baseMapper.selectPage(pageReqVO);
    }

}
