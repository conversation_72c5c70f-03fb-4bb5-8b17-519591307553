lengwang:
  info:
    base-package: com.lw.apcc.module.iot.data
  error-code: # 错误码相关配置项
    constants-class-list:
      - com.lw.apcc.module.iot.data.enums.ErrorCodeConstants
  tenant: # 多租户相关配置项
    ignore-urls:
      - /admin-api/iot-data/device/*
      - /admin-api/iot-data/device-test/*
      - /admin-api/iot-data/coldyun-iot-device/*
      - /admin-api/iot-data/statistics/*
      - /admin-api/iot-data/gps/get-address-location
      - /admin-api/iot-data/gps/get-address-locations
      - /admin-api/iot-data/gps/get-location-address
      - /admin-api/iot-data/gps/get-history/*
      - /admin-api/iot-data/weather/*
spring:
  application:
    name: iot-data-server
server:
  port: 48085

# MyBatis Plus 的配置项
mybatis-plus:
  # （自定义配置项） iot-data 排除方言限制，因为连接物联网有多种数据库
  dialect-enable: false

# 日志文件配置。注意，如果 logging.file.name 不放在 bootstrap.yaml 配置文件，而是放在 application 中，会导致出现 LOG_FILE_IS_UNDEFINED 文件
logging:
  file:
    name: ./logs/${spring.application.name}.log # 日志文件名，全路径
