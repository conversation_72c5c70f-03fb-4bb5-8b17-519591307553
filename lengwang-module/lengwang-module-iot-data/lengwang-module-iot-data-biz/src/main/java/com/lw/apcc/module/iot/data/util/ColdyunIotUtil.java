package com.lw.apcc.module.iot.data.util;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.lw.apcc.common.util.http.HttpClientUtil;
import com.lw.apcc.common.util.http.common.HttpConfig;
import com.lw.apcc.common.util.json.JsonUtils;
import com.lw.apcc.common.util.spring.SpringUtil;
import com.lw.apcc.module.iot.data.framework.lengwang.config.LengwangIotProperties;
import lombok.extern.slf4j.Slf4j;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR> yang
 * @description 憨云物联网平台
 * @date 2025/2/26 10:33
 */
@Slf4j
public class ColdyunIotUtil {

    /**
     * 冷王物联网平台配置
     */
    private static LengwangIotProperties iotProperties;

    static {
        iotProperties = SpringUtil.getBean(LengwangIotProperties.class);
//        iotProperties = new LengwangIotProperties();
    }

    /**
     * 根据设备编号查询设备实时信息
     *
     * @param deviceCode
     * @return
     */
    public static Map<String, Object> getRealtimeData(String deviceCode, String productId) {
        try {
            JSONArray realtimeList = listRealtimeData(Lists.newArrayList(deviceCode), productId);
            if (realtimeList != null && realtimeList.size() > 0) {
                return realtimeList.getJSONObject(0);
            }
        } catch (Exception ex) {
            ex.printStackTrace();
        }
        return Maps.newHashMap();
    }

    /**
     * 根据设备编号查询设备实时信息
     *
     * @param deviceCodes
     * @return
     */
    public static JSONArray listRealtimeData(List<String> deviceCodes, String productId) {
        try {
            log.info("========================================");
            log.info("物联网最新实时数据");
            String codes = JsonUtils.toJson(deviceCodes);
            log.info("设备：" + codes);
            String url = String.format("%s/api/device-latest-data/%s/query-device-data", iotProperties.getAddress(), productId);
            log.info("请求地址：" + url);
            HttpConfig config = HttpConfig.custom().url(url).json(codes).timeout(60000);
            String result = HttpClientUtil.post(config);
            log.info("结果：" + result);
            if (StrUtil.isNotEmpty(result) && result.contains("result") && result.contains("success")) {
                JSONArray dataList = JSONObject.parseObject(result).getJSONArray("result");
                if (dataList != null && dataList.size() > 0) {
                    return dataList;
                }
            }
        } catch (Exception ex) {
            ex.printStackTrace();
        } finally {
            log.info("========================================");
        }
        return null;
    }

    public static void main(String[] args) {
        System.out.println(listRealtimeData(Lists.newArrayList("240101002"), "1930147024035573760"));
    }

    /**
     * 根据设备编号查询历史数据信息
     *
     * @param deviceCode
     * @param startTime
     * @param endTime
     * @return
     */
    public static JSONArray listHistoryData(String deviceCode, long startTime, long endTime) {
        try {
            log.info("========================================");
            log.info("物联网平台历史数据");
            log.info("设备：" + deviceCode);
            String paramsFormat = "{\"paging\":false,\"sorts\":[{\"name\":\"timestamp\",\"order\":\"desc\"}],\"terms\":[{\"terms\":[{\"column\":\"timestamp\",\"termType\":\"btw\",\"value\":[%s,%s]}]}]}";
            String params = String.format(paramsFormat, startTime * 1000L, endTime * 1000L);
            log.info("请求参数：" + params);

            String url = String.format("%s/api/device/instance/%s/properties/_query/no-paging", iotProperties.getAddress(), deviceCode);
            log.info("请求地址：" + url);
            HttpConfig config = HttpConfig.custom().url(url).json(params).timeout(60000);
            String result = HttpClientUtil.post(config);
            log.info("结果：" + result);
            if (StrUtil.isNotEmpty(result) && result.contains("result") && result.contains("success")) {
                JSONArray dataList = JSONObject.parseObject(result).getJSONArray("result");
                if (dataList != null && dataList.size() > 0) {
                    return dataList;
                }
            }
        } catch (Exception ex) {
            ex.printStackTrace();
        } finally {
            log.info("========================================");
        }
        return null;
    }

}
