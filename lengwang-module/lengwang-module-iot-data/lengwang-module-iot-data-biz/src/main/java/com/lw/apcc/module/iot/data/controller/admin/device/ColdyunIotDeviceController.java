package com.lw.apcc.module.iot.data.controller.admin.device;

import com.lw.apcc.common.pojo.CommonResult;
import com.lw.apcc.module.iot.data.enums.api.ColdyunIotDeviceApiConstants;
import com.lw.apcc.module.iot.data.service.device.ColdyunIotDeviceService;
import com.lw.apcc.module.iot.data.vo.device.coldyunIot.device.DeviceHistoryDataReqVO;
import com.lw.apcc.module.iot.data.vo.device.coldyunIot.device.realtime.DeviceRealtimeDataRespVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.annotation.security.PermitAll;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

import static com.lw.apcc.common.pojo.CommonResult.success;
import static com.lw.apcc.module.iot.data.enums.api.ColdyunIotDeviceApiConstants.*;


@Tag(name = "憨云物联网平台对接接口")
@RestController
@RequestMapping(ColdyunIotDeviceApiConstants.PREFIX)
public class ColdyunIotDeviceController {

    @Resource
    private ColdyunIotDeviceService coldyunIotDeviceService;

    /**
     * 根据设备编号查询设备实时数据信息
     *
     * @param deviceId
     * @return
     */
    @GetMapping(GET_DEVICE_REALTIME_DATA_API)
    @PermitAll
    @Operation(summary = "根据设备编号查询探头实时数据信息")
    public CommonResult<DeviceRealtimeDataRespVO> getDeviceRealtimeData(@RequestParam("deviceId") String deviceId) {
        return success(coldyunIotDeviceService.getDeviceRealtimeData(deviceId));
    }

    /**
     * 根据设备编号查询探头历史数据信息
     *
     * @param reqVO
     * @return
     */
    @PostMapping(LIST_DEVICE_HISTORY_DATA_API)
    @PermitAll
    @Operation(summary = "根据设备编号查询探头历史数据信息")
    public CommonResult<List<Map>> listDeviceHistoryData(@RequestBody DeviceHistoryDataReqVO reqVO) {
        return success(coldyunIotDeviceService.listDeviceHistoryData(reqVO));
    }

    /**
     * 根据设备编号查询探头历史日志信息
     *
     * @param deviceId
     * @return
     */
    @GetMapping(LIST_DEVICE_HISTORY_LOG_API)
    @PermitAll
    @Operation(summary = "根据设备编号查询探头历史日志信息")
    public CommonResult<List<Map>> listDeviceHistoryLog(@RequestParam("deviceId") String deviceId) {
        return success(coldyunIotDeviceService.listDeviceHistoryLog(deviceId));
    }


}
