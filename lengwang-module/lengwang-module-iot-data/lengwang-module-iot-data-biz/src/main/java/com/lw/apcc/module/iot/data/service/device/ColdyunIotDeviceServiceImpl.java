package com.lw.apcc.module.iot.data.service.device;

import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.LocalDateTimeUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.lw.apcc.common.util.date.DateUtils;
import com.lw.apcc.common.util.http.HttpClientUtil;
import com.lw.apcc.common.util.http.common.HttpConfig;
import com.lw.apcc.module.iot.data.framework.lengwang.config.LengwangIotProperties;
import com.lw.apcc.module.iot.data.util.ColdyunIotUtil;
import com.lw.apcc.module.iot.data.vo.device.coldyunIot.device.DeviceHistoryDataReqVO;
import com.lw.apcc.module.iot.data.vo.device.coldyunIot.device.realtime.DeviceDetailsRespVO;
import com.lw.apcc.module.iot.data.vo.device.coldyunIot.device.realtime.DeviceRealtimeDataRespVO;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR> yang
 * @description 物联网平台 设备Service
 * @date 2025/7/1 10:08
 */
@Validated
@Service
@Slf4j
public class ColdyunIotDeviceServiceImpl implements ColdyunIotDeviceService {

    @Resource
    private LengwangIotProperties iotProperties;

    /**
     * @param deviceId
     * @description 查询设备实时数据
     * <AUTHOR> yang
     * @date 2025/7/1 10:56
     */
    @Override
    public DeviceRealtimeDataRespVO getDeviceRealtimeData(String deviceId) {

        // 设备产品详情
        DeviceDetailsRespVO deviceDetails = getDeviceDetails(deviceId);
        if (deviceDetails == null) {
            return null;
        }
        // 设备实时数据
        Map<String, Object> realtimeData = ColdyunIotUtil.getRealtimeData(deviceDetails.getId(), deviceDetails.getProductId());

        return new DeviceRealtimeDataRespVO(deviceDetails, realtimeData);
    }

    /**
     * @param deviceId
     * @description 获取设备详情
     * <AUTHOR> yang
     * @date 2025/7/1 10:56
     */
    private DeviceDetailsRespVO getDeviceDetails(String deviceId) {
        try {
            log.info("========================================");
            log.info("物联网平台设备详情数据");
            log.info("设备：" + deviceId);

            String url = String.format("%s/api/device-instance/%s/detail", iotProperties.getAddress(), deviceId);
            log.info("请求地址：" + url);
            HttpConfig config = HttpConfig.custom().url(url).timeout(60000);
            String result = HttpClientUtil.get(config);
            log.info("结果：" + result);
            if (StrUtil.isNotEmpty(result) && result.contains("result") && result.contains("success")) {
                DeviceDetailsRespVO deviceDetails = JSONObject.parseObject(result).getObject("result", DeviceDetailsRespVO.class);
                return deviceDetails;
            }
        } catch (Exception ex) {
            ex.printStackTrace();
        } finally {
            log.info("========================================");
        }
        return null;
    }

    /**
     * @param reqVO
     * @description 查询设备历史数据
     * <AUTHOR> yang
     * @date 2025/7/1 10:56
     */
    @Override
    public List<Map> listDeviceHistoryData(DeviceHistoryDataReqVO reqVO) {
        long startTime = DateUtils.parseEpochDay((ObjectUtil.defaultIfNull(reqVO.getStartTime(), LocalDateTimeUtil.beginOfDay(LocalDateTime.now()))));
        long endTime = DateUtils.parseEpochDay(ObjectUtil.defaultIfNull(reqVO.getEndTime(), LocalDateTimeUtil.endOfDay(LocalDateTime.now())));

        JSONArray historyDataList = ColdyunIotUtil.listHistoryData(reqVO.getDeviceId(), startTime, endTime);
        return convertHistoryData(historyDataList);
    }

    /**
     * 转换数据
     *
     * @param historyDatas
     * @return
     */
    private List<Map> convertHistoryData(JSONArray historyDatas) {
        if(historyDatas == null || historyDatas.size() == 0){
            return Lists.newArrayList();
        }

        List<Map> historyDataList = Lists.newLinkedList();
        for (int i = 0; i < historyDatas.size(); i++) {

            JSONObject historyData = historyDatas.getJSONObject(i);

            historyData.getString("createTime");
            Long createTime = historyData.getLong("createTime");
            if (createTime != null) {
                String time = Instant.ofEpochMilli(createTime)
                        .atZone(ZoneId.of("Asia/Shanghai"))
                        .format(DatePattern.NORM_DATETIME_FORMATTER);
                historyData.put("createTime", time);
            }

            historyDataList.add(historyData);
        }
        return historyDataList;
    }

    /**
     * 根据设备编号查询探头历史日志信息
     *
     * @param deviceId
     * @return
     */
    @Override
    public List<Map> listDeviceHistoryLog(String deviceId) {
        try {
            log.info("========================================");
            log.info("物联网平台历史日志");
            log.info("设备：" + deviceId);
            String params = "{\"paging\":true,\"sorts\":[{\"name\":\"timestamp\",\"order\":\"desc\"}]}";
            log.info("请求参数：" + params);

            String url = String.format("%s/api/device-instance/%s/log-contents", iotProperties.getAddress(), deviceId);
            log.info("请求地址：" + url);
            HttpConfig config = HttpConfig.custom().url(url).json(params).timeout(60000);
            String result = HttpClientUtil.post(config);
            log.info("结果：" + result);
            if (StrUtil.isNotEmpty(result) && result.contains("result") && result.contains("success") && result.contains("data")) {
                JSONArray logList = JSONObject.parseObject(result).getJSONObject("result").getJSONArray("data");
                if (logList != null && logList.size() > 0) {
                    return logList.toJavaList(Map.class);
                }
            }
        } catch (Exception ex) {
            ex.printStackTrace();
        } finally {
            log.info("========================================");
        }

        return List.of();
    }


}
