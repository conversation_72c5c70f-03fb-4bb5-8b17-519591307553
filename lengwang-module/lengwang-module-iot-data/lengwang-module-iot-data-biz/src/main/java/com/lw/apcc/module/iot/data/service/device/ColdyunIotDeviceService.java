package com.lw.apcc.module.iot.data.service.device;

import com.lw.apcc.module.iot.data.vo.device.coldyunIot.device.DeviceHistoryDataReqVO;
import com.lw.apcc.module.iot.data.vo.device.coldyunIot.device.realtime.DeviceRealtimeDataRespVO;

import java.util.List;
import java.util.Map;

/**
 * 物联网平台 设备Service
 */
public interface ColdyunIotDeviceService {

    /**
     * @description 查询设备实时数据
     * <AUTHOR> yang
     * @date 2025/7/1 10:56
     */
    DeviceRealtimeDataRespVO getDeviceRealtimeData(String deviceId);

    /**
     * @description 查询设备历史数据
     * <AUTHOR> yang
     * @date 2025/7/1 10:56
     */
    List<Map> listDeviceHistoryData(DeviceHistoryDataReqVO reqVO);

    /**
     * 根据设备编号查询探头历史日志信息
     *
     * @param deviceId
     * @return
     */
    List<Map> listDeviceHistoryLog(String deviceId);

}
