package com.lw.apcc.module.iot.data.service.product.impl.dxhua;

import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.LocalDateTimeUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.lw.apcc.common.util.date.DateUtils;
import com.lw.apcc.common.util.http.HttpClientUtil;
import com.lw.apcc.common.util.http.common.HttpConfig;
import com.lw.apcc.module.iot.data.constants.LengwangIotProductConstant;
import com.lw.apcc.module.iot.data.framework.lengwang.config.LengwangIotProperties;
import com.lw.apcc.module.iot.data.service.product.ProductService;
import com.lw.apcc.module.iot.data.util.ColdyunIotUtil;
import com.lw.apcc.module.iot.data.vo.device.history.DeviceHistoryDataRespVO;
import com.lw.apcc.module.iot.data.vo.device.realtime.DeviceRealtimeDataRespVO;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR> yang
 * @description HY-APT100 (冷库智能监测仪)
 * @date 2025/2/25 20:41
 */
@Service
@Slf4j
public class ProductHYAPT100ServiceImpl extends ProductService {

    @Resource
    private LengwangIotProperties iotProperties;

    /**
     * 根据设备编号查询设备实时信息
     *
     * @param deviceCodes
     * @return
     */
    @Override
    public List<DeviceRealtimeDataRespVO> listRealtimeData(List<String> deviceCodes, Map<String, Object> extendParams) {
        JSONArray dataList = ColdyunIotUtil.listRealtimeData(deviceCodes, LengwangIotProductConstant.HY_APT100_PRODUCT_ID);
        if (dataList != null && dataList.size() > 0) {
            List<DeviceRealtimeDataRespVO> realtimeList = convertDeviceRealtime(dataList);
            return realtimeList;
        }
        return Lists.newArrayList();
    }

    /**
     * 将设备数据转换成设备实时数据
     *
     * @param deviceDataList
     * @return
     */
    private List<DeviceRealtimeDataRespVO> convertDeviceRealtime(JSONArray deviceDataList) {
        List<DeviceRealtimeDataRespVO> deviceRealtimeList = Lists.newArrayList();

        for (int i = 0; i < deviceDataList.size(); i++) {
            JSONObject deviceData = deviceDataList.getJSONObject(i);

            DeviceRealtimeDataRespVO realtimeData = new DeviceRealtimeDataRespVO();

            realtimeData.setDeviceCode(deviceData.getString("id"));
            realtimeData.setValue(deviceData.getString("temperature"));

            StringBuilder note = new StringBuilder("");
            String temperature = deviceData.getString("temperature");
            if (StrUtil.isNotEmpty(temperature)) {
                note.append(temperature);
                note.append("℃");
            }
            String humidity = deviceData.getString("humidity");
            if (StrUtil.isNotEmpty(humidity)) {
                note.append("，");
                note.append(humidity);
                note.append("%");
            }
            realtimeData.setNote(note.toString());
            realtimeData.setExtend(deviceData.toJSONString());

            LocalDateTime time = null;
            Long timestamp = deviceData.getLong("timestamp");
            if (timestamp != null) {
                time = Instant.ofEpochMilli(timestamp)
                        .atZone(ZoneId.of("Asia/Shanghai"))
                        .toLocalDateTime();
            } else {
                log.info("设备：{}，时间获取错误", deviceData.getString("id"));
            }
            realtimeData.setTime(time);

            realtimeData.setMeta(deviceData.toJSONString());

            deviceRealtimeList.add(realtimeData);
        }
        return deviceRealtimeList;
    }


    /**
     * 根据设备编号查询历史数据信息
     *
     * @param deviceCode
     * @param startTime
     * @param endTime
     * @param extendParams
     * @return
     */
    @Override
    public List<DeviceHistoryDataRespVO> listHistoryData(String deviceCode, long startTime, long endTime, Map<String, Object> extendParams) {
        JSONArray elitechDataList = ColdyunIotUtil.listHistoryData(deviceCode, startTime, endTime);
        if (elitechDataList != null && elitechDataList.size() > 0) {
            List<DeviceHistoryDataRespVO> historyList = convertDeviceHistory(elitechDataList);
            return historyList;
        }
        return Lists.newArrayList();
    }

    /**
     * 将设备数据转换成设备历史数据
     *
     * @param dataList
     * @return
     */
    private static List<DeviceHistoryDataRespVO> convertDeviceHistory(JSONArray dataList) {
        List<DeviceHistoryDataRespVO> deviceHistoryList = Lists.newArrayList();

        for (int i = 0; i < dataList.size(); i++) {
            JSONObject deviceData = dataList.getJSONObject(i);
            DeviceHistoryDataRespVO historyData = new DeviceHistoryDataRespVO();

            historyData.setDeviceCode(deviceData.getString("deviceId"));
            historyData.setValue(deviceData.getString("temperature"));

            StringBuilder note = new StringBuilder("");
            String temperature = deviceData.getString("temperature");
            if (StrUtil.isNotEmpty(temperature)) {
                note.append(temperature);
                note.append("℃");
            }
            String humidity = deviceData.getString("humidity");
            if (StrUtil.isNotEmpty(humidity)) {
                note.append("，");
                note.append(humidity);
                note.append("%");
            }
            historyData.setNote(note.toString());
            historyData.setExtend(deviceData.toJSONString());

            String time = null;
            Long createTime = deviceData.getLong("createTime");
            if (createTime != null) {
                time = Instant.ofEpochMilli(createTime)
                        .atZone(ZoneId.of("Asia/Shanghai"))
                        .format(DatePattern.NORM_DATETIME_FORMATTER);
            } else {
                log.info("设备：{}，时间获取错误", deviceData.getString("deviceId"));
                return null;
            }
            historyData.setTime(time);

            deviceData.put("createTime", time);
            historyData.setMeta(JSONObject.toJSONString(deviceData));

            deviceHistoryList.add(historyData);
        }
        return deviceHistoryList;
    }

    public static void main(String[] args) {
        try {
            String deviceCode = "25960608001";
            long startTime = DateUtils.parseEpochDay((ObjectUtil.defaultIfNull(LocalDateTime.parse("2025-06-25T11:04:03"), LocalDateTimeUtil.beginOfDay(LocalDateTime.now()))));
            long endTime = DateUtils.parseEpochDay(ObjectUtil.defaultIfNull(LocalDateTime.parse("2025-06-26T11:04:03"), LocalDateTimeUtil.endOfDay(LocalDateTime.now())));

            log.info("========================================");
            log.info("物联网平台历史数据");
            log.info("设备：" + deviceCode);
            String paramsFormat = "{\"sorts\":[{\"name\":\"timestamp\",\"order\":\"asc\"}],\"terms\":[{\"terms\":[{\"column\":\"timestamp\",\"termType\":\"btw\",\"value\":[%s,%s]}]}]}";
            String params = String.format(paramsFormat, startTime * 1000L, endTime * 1000L);

            String url = String.format("%s/api/device/instance/%s/properties/_query/no-paging", "https://iot-hanyun.coldyun.net", deviceCode);
            log.info("请求地址：" + url);
            HttpConfig config = HttpConfig.custom().url(url).json(params).timeout(60000);
            String result = HttpClientUtil.post(config);
            log.info("结果：" + result);
            if (StrUtil.isNotEmpty(result) && result.contains("result") && result.contains("success")) {
                JSONArray elitechDataList = JSONObject.parseObject(result).getJSONArray("result");
                if (elitechDataList != null && elitechDataList.size() > 0) {
                    List<DeviceHistoryDataRespVO> historyList = convertDeviceHistory(elitechDataList);
                    System.out.println(JSONObject.toJSONString(historyList));
                }
            }
        } catch (Exception ex) {
            ex.printStackTrace();
        } finally {
            log.info("========================================");
        }
    }


}
