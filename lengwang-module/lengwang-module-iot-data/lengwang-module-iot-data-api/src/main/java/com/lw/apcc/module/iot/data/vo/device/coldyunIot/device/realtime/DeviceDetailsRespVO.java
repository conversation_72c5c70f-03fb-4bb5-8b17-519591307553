package com.lw.apcc.module.iot.data.vo.device.coldyunIot.device.realtime;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;
import java.util.Map;

@Schema(description = "设备详细信息 Response VO")
@Data
public class DeviceDetailsRespVO {

    @Schema(description = "设备ID")
    private String id;

    @Schema(description = "设备名称")
    private String name;

    @Schema(description = "设备图片URL")
    private String photoUrl;

    @Schema(description = "协议ID")
    private String protocol;

    @Schema(description = "协议名称")
    private String protocolName;

    @Schema(description = "传输方式")
    private String transport;

    @Schema(description = "产品ID")
    private String productId;

    @Schema(description = "产品名称")
    private String productName;

    @Schema(description = "设备类型")
    private DeviceTypeRespVO deviceType;

    @Schema(description = "设备状态")
    private DeviceStateRespVO state;

    @Schema(description = "设备地址")
    private String address;

    @Schema(description = "上线时间")
    private Long onlineTime;

    @Schema(description = "离线时间")
    private Long offlineTime;

    @Schema(description = "创建时间")
    private Long createTime;

    @Schema(description = "注册时间")
    private Long registerTime;

    @Schema(description = "设备元数据JSON")
    private String metadata;

    @Schema(description = "产品元数据JSON")
    private String productMetadata;

    @Schema(description = "是否独立元数据")
    private Boolean independentMetadata;

    @Schema(description = "设备配置")
    private DeviceConfigurationRespVO configuration;

    @Schema(description = "缓存配置")
    private Map<String, Object> cachedConfiguration;

    @Schema(description = "是否独立配置")
    private Boolean aloneConfiguration;

    @Schema(description = "标签列表")
    private List<String> tags;

    @Schema(description = "接入ID")
    private String accessId;

    @Schema(description = "接入提供者")
    private String accessProvider;

    @Schema(description = "接入名称")
    private String accessName;

    @Schema(description = "分类ID")
    private String classifiedId;

    @Schema(description = "分类名称")
    private String classifiedName;
}
