package com.lw.apcc.module.iot.data.vo.device.coldyunIot.device.realtime;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Map;

@Schema(description = "设备属性 Response VO")
@Data
public class DevicePropertyRespVO {

    @Schema(description = "属性ID")
    private String id;

    @Schema(description = "属性名称")
    private String name;

    @Schema(description = "扩展信息")
    private Map<String, Object> expands;

    @Schema(description = "值类型")
    private ValueTypeRespVO valueType;
}
