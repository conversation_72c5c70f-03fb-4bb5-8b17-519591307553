package com.lw.apcc.module.iot.data.vo.device.coldyunIot.device.realtime;


import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Map;

@Schema(description = "RPC 服务 - 物联网设备探头测试实时数据 Response VO")
@Data
@AllArgsConstructor
@NoArgsConstructor
public class DeviceRealtimeDataRespVO {

    @Schema(description = "设备详细信息")
    private DeviceDetailsRespVO deviceDetails;

    @Schema(description = "属性信息")
    private Map<String, Object> realtimeData;

}
