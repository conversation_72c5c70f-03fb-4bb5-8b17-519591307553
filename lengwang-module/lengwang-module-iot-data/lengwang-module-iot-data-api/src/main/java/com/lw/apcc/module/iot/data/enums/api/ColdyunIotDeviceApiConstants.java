package com.lw.apcc.module.iot.data.enums.api;

/**
 * 憨云物联网平台设备API常亮
 */
public interface ColdyunIotDeviceApiConstants {

    /**
     * API 前缀
     */
    String PREFIX = ApiConstants.MODEL_PREFIX + "/coldyun-iot-device";


    // ---------------------------------------------------------
    // -------------物联网API------------------------------------
    // ---------------------------------------------------------
    /**
     * 根据设备编号查询设备实时数据获取API
     */
    String GET_DEVICE_REALTIME_DATA_API = "/get-device-realtime-data";
    /**
     * 根据设备编号查询设备历史数据信息API
     */
    String LIST_DEVICE_HISTORY_DATA_API = "/list-device-history-data";
    /**
     * 根据设备编号查询设备日志数据信息API
     */
    String LIST_DEVICE_HISTORY_LOG_API = "/list-device-history-log";

}
