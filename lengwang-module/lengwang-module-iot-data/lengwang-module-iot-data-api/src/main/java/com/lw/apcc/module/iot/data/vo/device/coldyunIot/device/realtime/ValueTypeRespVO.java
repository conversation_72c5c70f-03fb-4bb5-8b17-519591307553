package com.lw.apcc.module.iot.data.vo.device.coldyunIot.device.realtime;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

@Schema(description = "值类型 Response VO")
@Data
public class ValueTypeRespVO {

    @Schema(description = "类型")
    private String type;

    @Schema(description = "单位")
    private String unit;

    @Schema(description = "精度")
    private Integer scale;

    @Schema(description = "枚举元素")
    private List<EnumElementRespVO> elements;
}
