package com.lw.apcc.module.iot.data.vo.device.coldyunIot.device;

import cn.hutool.core.date.DatePattern;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

@Schema(description = "RPC 服务 - 物联网设备测试设备历史数据 Request VO")
@Data
public class DeviceHistoryDataRespVO {

    @Schema(description = "传感器ID")
    private String sensorId;

    @Schema(description = "传感器序列号")
    private String sensorSn;

    @Schema(description = "中继器ID")
    private String senderId;

    @Schema(description = "发送器序列号")
    private String senderSn;

    @Schema(description = "温度")
    private String temp;

    @Schema(description = "湿度")
    private String humi;

    @Schema(description = "电压")
    private String volt;

    @Schema(description = "信号强度【-30到-75（-100最差）】")
    private String rssi;

    @Schema(description = "传感器采集时间")
    @JsonFormat(pattern = DatePattern.NORM_DATETIME_PATTERN)
    private LocalDateTime sensorCollectTime;

    @Schema(description = "传感器传输时间")
    @JsonFormat(pattern = DatePattern.NORM_DATETIME_PATTERN)
    private LocalDateTime sensorTransTime;

    @Schema(description = "发送器传输时间")
    @JsonFormat(pattern = DatePattern.NORM_DATETIME_PATTERN)
    private LocalDateTime senderTransTime;

    @Schema(description = "系统时间")
    @JsonFormat(pattern = DatePattern.NORM_DATETIME_PATTERN)
    private LocalDateTime systemTime;

    @Schema(description = "是否补传")
    private Boolean isadd;

}
