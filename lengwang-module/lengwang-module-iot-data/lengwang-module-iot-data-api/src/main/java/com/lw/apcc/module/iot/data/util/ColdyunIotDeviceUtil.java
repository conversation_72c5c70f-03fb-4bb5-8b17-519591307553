package com.lw.apcc.module.iot.data.util;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.fasterxml.jackson.core.type.TypeReference;
import com.google.common.collect.Lists;
import com.lw.apcc.common.pojo.CommonResult;
import com.lw.apcc.common.util.http.HttpRequest;
import com.lw.apcc.common.util.json.JsonUtils;
import com.lw.apcc.module.iot.data.base.BaseUtil;
import com.lw.apcc.module.iot.data.enums.api.ColdyunIotDeviceApiConstants;
import com.lw.apcc.module.iot.data.vo.device.coldyunIot.device.DeviceHistoryDataReqVO;
import com.lw.apcc.module.iot.data.vo.device.coldyunIot.device.realtime.DeviceRealtimeDataRespVO;
import lombok.extern.slf4j.Slf4j;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR> yang
 * @description 憨云物联网设备数据工具类
 * @date 2024/2/28 10:51
 */
@Slf4j
public class ColdyunIotDeviceUtil extends BaseUtil {

    /**
     * 根据设备编号查询设备实时数据信息
     *
     * @param deviceId 设备ID
     * @return 设备实时数据信息
     */
    public static DeviceRealtimeDataRespVO getDeviceRealtimeData(String deviceId) {
        try {
            log.info("--------------------------------[憨云设备实时数据获取]----------------------------------------");
            log.info(String.format("设备编号：%s", deviceId));

            // 地址
            String url = getAddress() + ColdyunIotDeviceApiConstants.PREFIX + ColdyunIotDeviceApiConstants.GET_DEVICE_REALTIME_DATA_API;
            log.info(String.format("url：%s", url));
            
            // 参数
            String param = String.format("deviceId=%s", deviceId);
            log.info(String.format("param：%s", param));

            String resultJson = HttpRequest.sendGet(url, param);
            log.info(String.format("result：%s", resultJson));
            log.info(String.format("日期：%s", DateUtil.formatLocalDateTime(LocalDateTime.now())));

            if (StrUtil.isNotEmpty(resultJson) && resultJson.contains("data")) {
                CommonResult<DeviceRealtimeDataRespVO> result = JsonUtils.parseObject(resultJson, new TypeReference<CommonResult<DeviceRealtimeDataRespVO>>() {
                });
                if (result.isSuccess()) {
                    return result.getData();
                }
            }
        } catch (Exception ex) {
            log.error("获取憨云设备实时数据异常", ex);
        } finally {
            log.info("------------------------------------------------------------------------");
        }
        return null;
    }

    /**
     * 根据设备编号查询探头历史数据信息
     *
     * @param reqVO 历史数据查询请求VO
     * @return 探头历史数据列表
     */
    public static List<Map> listDeviceHistoryData(DeviceHistoryDataReqVO reqVO) {
        try {
            log.info("--------------------------------[憨云设备历史数据获取]----------------------------------------");
            log.info(String.format("请求参数：%s", JsonUtils.toJsonString(reqVO)));

            // 地址
            String url = getAddress() + ColdyunIotDeviceApiConstants.PREFIX + ColdyunIotDeviceApiConstants.LIST_DEVICE_HISTORY_DATA_API;
            log.info(String.format("url：%s", url));

            // 发送POST请求
            String resultJson = HttpRequest.post(url, JsonUtils.toJsonString(reqVO));
            log.info(String.format("result：%s", resultJson));
            log.info(String.format("日期：%s", DateUtil.formatLocalDateTime(LocalDateTime.now())));

            if (StrUtil.isNotEmpty(resultJson) && resultJson.contains("data")) {
                CommonResult<List<Map>> result = JsonUtils.parseObject(resultJson, new TypeReference<CommonResult<List<Map>>>() {
                });
                if (result.isSuccess()) {
                    return result.getData();
                }
            }
        } catch (Exception ex) {
            log.error("获取憨云设备历史数据异常", ex);
        } finally {
            log.info("------------------------------------------------------------------------");
        }
        return Lists.newArrayList();
    }

    /**
     * 根据设备编号查询探头历史日志信息
     *
     * @param deviceId 设备ID
     * @return 探头历史日志列表
     */
    public static List<Map> listDeviceHistoryLog(String deviceId) {
        try {
            log.info("--------------------------------[憨云设备历史日志获取]----------------------------------------");
            log.info(String.format("设备编号：%s", deviceId));

            // 地址
            String url = getAddress() + ColdyunIotDeviceApiConstants.PREFIX + ColdyunIotDeviceApiConstants.LIST_DEVICE_HISTORY_LOG_API;
            log.info(String.format("url：%s", url));
            
            // 参数
            String param = String.format("deviceId=%s", deviceId);
            log.info(String.format("param：%s", param));

            String resultJson = HttpRequest.sendGet(url, param);
            log.info(String.format("result：%s", resultJson));
            log.info(String.format("日期：%s", DateUtil.formatLocalDateTime(LocalDateTime.now())));

            if (StrUtil.isNotEmpty(resultJson) && resultJson.contains("data")) {
                CommonResult<List<Map>> result = JsonUtils.parseObject(resultJson, new TypeReference<CommonResult<List<Map>>>() {
                });
                if (result.isSuccess()) {
                    return result.getData();
                }
            }
        } catch (Exception ex) {
            log.error("获取憨云设备历史日志异常", ex);
        } finally {
            log.info("------------------------------------------------------------------------");
        }
        return Lists.newArrayList();
    }

    public static void main(String[] args) {
        // 测试获取设备实时数据
        System.out.println("=== 测试获取设备实时数据 ===");
        System.out.println(getDeviceRealtimeData("240101002"));

        // 测试获取设备历史数据
        System.out.println("=== 测试获取设备历史数据 ===");
        DeviceHistoryDataReqVO historyReqVO = DeviceHistoryDataReqVO.builder()
                .deviceId("240101002")
                .startTime(LocalDateTime.now().minusDays(1))
                .endTime(LocalDateTime.now())
                .build();
        System.out.println(listDeviceHistoryData(historyReqVO));

        // 测试获取设备历史日志
        System.out.println("=== 测试获取设备历史日志 ===");
        System.out.println(listDeviceHistoryLog("240101002"));
    }
}
