package com.lw.apcc.module.iot.data.vo.device.coldyunIot.device;

import cn.hutool.core.date.DatePattern;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Builder;
import lombok.Data;

import java.time.LocalDateTime;

@Schema(description = "RPC 服务 - 物联网设备测试历史数据 Request VO")
@Data
@Builder
public class DeviceHistoryDataReqVO {

    @Schema(description = "设备ID")
    @NotNull(message = "设备ID必填")
    private String deviceId;

    @Schema(description = "开始时间")
    @JsonFormat(pattern = DatePattern.NORM_DATETIME_PATTERN)
    private LocalDateTime startTime;

    @Schema(description = "结束时间")
    @JsonFormat(pattern = DatePattern.NORM_DATETIME_PATTERN)
    private LocalDateTime endTime;


}
