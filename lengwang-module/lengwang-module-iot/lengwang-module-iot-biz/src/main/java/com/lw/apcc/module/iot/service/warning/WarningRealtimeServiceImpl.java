package com.lw.apcc.module.iot.service.warning;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.lw.apcc.common.pojo.PageResult;
import com.lw.apcc.common.security.SecurityUtils;
import com.lw.apcc.common.util.date.DateUtils;
import com.lw.apcc.framework.mybatis.core.service.BaseServiceImpl;
import com.lw.apcc.framework.mybatis.core.util.MyBatisUtils;
import com.lw.apcc.module.iot.api.sensor.dto.SensorRespDTO;
import com.lw.apcc.module.iot.cache.SensorCache;
import com.lw.apcc.module.iot.cache.WarningTypeCache;
import com.lw.apcc.module.iot.controller.admin.warning.vo.WarningRealtimePageReqVO;
import com.lw.apcc.module.iot.controller.admin.warning.vo.WarningRealtimeRespVO;
import com.lw.apcc.module.iot.controller.app.warning.vo.AppWarningHomeTipsRespVO;
import com.lw.apcc.module.iot.dal.dataobject.warning.WarningRealtimeDO;
import com.lw.apcc.module.iot.dal.mapper.warning.WarningRealtimeMapper;
import com.lw.apcc.module.iot.enums.WarningRangeTypeEnum;
import com.lw.apcc.module.iot.enums.warning.WarningTypeConstants;
import com.lw.apcc.module.iot.utils.ProductWarningSettingUtil;
import com.lw.apcc.module.system.api.permission.dto.DataPermissionRespDTO;
import com.lw.apcc.module.system.cache.DataPermissionCache;
import com.lw.apcc.module.system.enums.permission.DataPermissionCodeEnum;
import org.apache.commons.compress.utils.Lists;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import java.time.LocalDateTime;
import java.util.Collections;
import java.util.List;

import static com.lw.apcc.common.exception.util.ServiceExceptionUtil.exception;

/**
 * 预警实时信息 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class WarningRealtimeServiceImpl extends BaseServiceImpl<WarningRealtimeMapper, WarningRealtimeDO> implements WarningRealtimeService {


    @Override
    public WarningRealtimeRespVO getWarningRealtime(Long id) {
        return baseMapper.getWarningRealtimeOne(id);
    }

    @Override
    public PageResult<WarningRealtimeRespVO> getWarningRealtimePage(WarningRealtimePageReqVO pageReqVO) {
        DataPermissionRespDTO dataPermission = DataPermissionCache.getDataPermissionByCode(DataPermissionCodeEnum.UNIT);

        return MyBatisUtils.buildPageResult(baseMapper.getWarningRealtimePage(MyBatisUtils.buildPage(pageReqVO), pageReqVO, dataPermission));
    }

    @Override
    public List<WarningRealtimeRespVO> getWarningRealtimeList(WarningRealtimePageReqVO pageReqVO) {
        DataPermissionRespDTO dataPermission = DataPermissionCache.getDataPermissionByCode(DataPermissionCodeEnum.UNIT);

        return baseMapper.getWarningRealtimeList(pageReqVO, dataPermission);
    }

    /**
     * 根据预警id查询实时预警信息
     *
     * @param warningId
     * @return
     */
    @Override
    public WarningRealtimeDO getOneByWarningId(Long warningId, Long deviceId) {
        List<WarningRealtimeDO> realtimeList = list(Wrappers.<WarningRealtimeDO>lambdaQuery().eq(WarningRealtimeDO::getWarningId, warningId).eq(WarningRealtimeDO::getDeviceId, deviceId));
        if (ObjectUtil.isNotEmpty(realtimeList)) {
            return realtimeList.get(0);
        }
        return null;
    }

    /**
     * 查询首页预警提示集合
     *
     * @return
     */
    @Override
    public List<AppWarningHomeTipsRespVO> listWarningHomeTips() {
        List<Long> unitIdList = SecurityUtils.getUnitId();

        if (ObjectUtil.isNotEmpty(unitIdList)) {
            List<AppWarningHomeTipsRespVO> homeTipsList = baseMapper.listWarningHomeTips(unitIdList);

            if (CollectionUtils.isNotEmpty(homeTipsList)) {
                buildHomeTips(homeTipsList);
                return homeTipsList;
            }
        }

        return Lists.newArrayList();
    }

    /**
     * 构建首页预警提示集合
     *
     * @param homeTipsList
     */
    private void buildHomeTips(List<AppWarningHomeTipsRespVO> homeTipsList) {
        homeTipsList.forEach(
                homeTips -> {
                    // 名称
                    String name = ProductWarningSettingUtil.convertName(homeTips.getSettingType(), homeTips.getWarningTypeId(), homeTips.getSensorId(), homeTips.getRangeType());
                    homeTips.setName(name);

                    // 提示内容
                    StringBuffer content = new StringBuffer("");

                    // 时间
                    content.append(DateUtils.format(LocalDateTime.now(), "MM月dd日 HH:mm"));
                    content.append(" ");
                    // 资产
                    content.append(homeTips.getAssetName());

                    // 内容
                    // 定制逻辑
                    if (WarningTypeConstants.RULE_TYPE_CUSTOM.equals(homeTips.getRuleType())) {
                        content.append("发生");
                        content.append(name);
                    }
                    // 常规逻辑
                    else if (WarningTypeConstants.RULE_TYPE_GENERAL.equals(homeTips.getRuleType())) {
                        SensorRespDTO sensor = SensorCache.getById(homeTips.getSensorId());
                        content.append(sensor.getName());
                        content.append("发生");
                        content.append(name);
                        content.append("，");
                        content.append("当前");
                        content.append(sensor.getName());
                        content.append(homeTips.getWarningValue());
                        content.append(sensor.getUnit());
                    }
                    content.append("，请及时处理！");

                    homeTips.setContent(content.toString());
                }
        );
    }

    /**
     * 根据设备id查询预警实时集合
     *
     * @param deviceId     设备ID
     * @param notWarningId 排除预警ID
     * @return
     */
    @Override
    public List<WarningRealtimeDO> listByDeviceId(Long deviceId, Long notWarningId) {
        return list(Wrappers.<WarningRealtimeDO>lambdaQuery().eq(WarningRealtimeDO::getDeviceId, deviceId).ne(WarningRealtimeDO::getWarningId, notWarningId));
    }

    /**
     * 获取小程序实时预警消息数量
     *
     * @return
     */
    @Override
    public Long getCurrentUserWarningRealtimeCount() {

        Long unitId = SecurityUtils.getDefaultUnitId();
        List<Long> unitIdList = Lists.newArrayList();
        unitIdList.add(unitId);

        List<AppWarningHomeTipsRespVO> homeTipsList = baseMapper.listWarningHomeTips(unitIdList);

        if (CollectionUtils.isNotEmpty(homeTipsList)) {
            return (long) homeTipsList.size();
        }

        return 0L;
    }

    /**
     * 获取当前用户预警实时信息列表
     *
     * @return
     */
    @Override
    public List<AppWarningHomeTipsRespVO> getCurrentUserWarningRealtimeList() {
        Long unitId = SecurityUtils.getDefaultUnitId();
        List<Long> unitIdList = Lists.newArrayList();
        unitIdList.add(unitId);

        List<AppWarningHomeTipsRespVO> homeTipsList = baseMapper.listWarningHomeTips(unitIdList);

        if (CollectionUtils.isNotEmpty(homeTipsList)) {
            buildHomeTips(homeTipsList);
            return homeTipsList;
        }

        return Collections.emptyList();
    }

}
