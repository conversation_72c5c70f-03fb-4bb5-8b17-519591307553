package com.lw.apcc.module.iot.service.device;

import com.lw.apcc.module.iot.data.vo.device.coldyunIot.device.DeviceHistoryDataReqVO;
import com.lw.apcc.module.iot.data.vo.device.coldyunIot.device.realtime.DeviceRealtimeDataRespVO;

import java.util.List;
import java.util.Map;

/**
 * 憨云物联网设备 Service 接口
 *
 * <AUTHOR>
 */
public interface AppColdyunIotDeviceService {

    /**
     * 获取设备实时数据
     *
     * @param deviceId 设备ID
     * @return 设备实时数据
     */
    DeviceRealtimeDataRespVO getDeviceRealtimeData(String deviceId);

    /**
     * 获取设备历史数据列表
     *
     * @param reqVO 请求参数
     * @return 设备历史数据列表
     */
    List<Map> listDeviceHistoryData(DeviceHistoryDataReqVO reqVO);

    /**
     * 获取设备历史日志列表
     *
     * @param deviceId 设备ID
     * @return 设备历史日志列表
     */
    List<Map> listDeviceHistoryLog(String deviceId);

} 