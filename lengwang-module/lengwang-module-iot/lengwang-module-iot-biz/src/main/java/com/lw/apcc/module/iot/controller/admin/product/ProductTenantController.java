package com.lw.apcc.module.iot.controller.admin.product;

import com.lw.apcc.common.pojo.CommonResult;
import com.lw.apcc.module.iot.controller.admin.product.vo.ProductTenantSaveReqVO;
import com.lw.apcc.module.iot.service.product.ProductTenantService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.Set;

import static com.lw.apcc.common.pojo.CommonResult.success;

@Tag(name = "管理后台 - 产品租户关联信息")
@RestController
@RequestMapping("/iot/product-tenant")
@Validated
public class ProductTenantController {

    @Resource
    private ProductTenantService productTenantService;

    @GetMapping("/get-product-id-by-tenant-id")
    @Operation(summary = "根据租户ID查询产品ID")
    public CommonResult<Set<Long>> getProductIdsByTenantId(@RequestParam("tenantId") Long tenantId) {
        return success(productTenantService.getProductIdsByTenantId(tenantId));
    }

    @PostMapping("/save-by-tenant-id")
    @Operation(summary = "根据租户id批量保存租户id")
    public CommonResult<Boolean> saveByTenantId(@Valid @RequestBody ProductTenantSaveReqVO saveReqVO) {
        return success(productTenantService.saveByTenantId(saveReqVO));
    }


}
