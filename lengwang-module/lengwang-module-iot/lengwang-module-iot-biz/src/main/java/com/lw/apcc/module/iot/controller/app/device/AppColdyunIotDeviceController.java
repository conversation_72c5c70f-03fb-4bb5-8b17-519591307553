package com.lw.apcc.module.iot.controller.app.device;

import com.lw.apcc.common.pojo.CommonResult;
import com.lw.apcc.module.iot.data.vo.device.coldyunIot.device.DeviceHistoryDataReqVO;
import com.lw.apcc.module.iot.data.vo.device.coldyunIot.device.realtime.DeviceRealtimeDataRespVO;
import com.lw.apcc.module.iot.service.device.AppColdyunIotDeviceService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

import static com.lw.apcc.common.pojo.CommonResult.success;

@Tag(name = "App 后台 - 憨云物联网设备信息")
@RestController
@RequestMapping("/iot/coldyun-iot-device")
@Validated
public class AppColdyunIotDeviceController {

    @Resource
    private AppColdyunIotDeviceService appColdyunIotDeviceService;

    /**
     * 根据设备编号查询设备实时数据信息
     *
     * @param deviceId 设备ID
     * @return
     */
    @GetMapping("/get-device-realtime-data")
    @Operation(summary = "根据设备编号查询设备实时数据信息")
    public CommonResult<DeviceRealtimeDataRespVO> getDeviceRealtimeData(@RequestParam("deviceId") String deviceId) {
        return success(appColdyunIotDeviceService.getDeviceRealtimeData(deviceId));
    }

    /**
     * 根据设备编号查询探头历史数据信息
     *
     * @param reqVO
     * @return
     */
    @PostMapping("/list-device-history-data")
    @Operation(summary = "根据设备编号查询探头历史数据信息")
    public CommonResult<List<Map>> listDeviceHistoryData(@RequestBody DeviceHistoryDataReqVO reqVO) {
        return success(appColdyunIotDeviceService.listDeviceHistoryData(reqVO));
    }

    /**
     * 根据设备编号查询探头历史日志信息
     *
     * @param deviceId 设备ID
     * @return
     */
    @GetMapping("/list-device-history-log")
    @Operation(summary = "根据设备编号查询探头历史日志信息")
    public CommonResult<List<Map>> listDeviceHistoryLog(@RequestParam("deviceId") String deviceId) {
        return success(appColdyunIotDeviceService.listDeviceHistoryLog(deviceId));
    }

} 