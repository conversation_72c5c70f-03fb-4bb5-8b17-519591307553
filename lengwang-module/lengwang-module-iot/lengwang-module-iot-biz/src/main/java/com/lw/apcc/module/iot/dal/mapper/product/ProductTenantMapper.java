package com.lw.apcc.module.iot.dal.mapper.product;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.lw.apcc.common.util.object.ObjectUtil;
import com.lw.apcc.framework.mybatis.core.mapper.BaseMapperX;
import com.lw.apcc.module.iot.dal.dataobject.product.ProductTenantDO;
import org.apache.ibatis.annotations.Mapper;

import java.util.Collection;

/**
 * 产品租户关联信息 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface ProductTenantMapper extends BaseMapperX<ProductTenantDO> {


    /**
     * 批量删除
     *
     * @param tenantId
     * @param productIds
     * @return
     */
    default int removeByTenantId(Long tenantId, Collection<Long> productIds) {
        if (ObjectUtil.isEmpty(productIds)) {
            return 0;
        }
        return delete(Wrappers.<ProductTenantDO>lambdaQuery().eq(ProductTenantDO::getTenantId, tenantId).in(ProductTenantDO::getProductId, productIds));
    }

}
