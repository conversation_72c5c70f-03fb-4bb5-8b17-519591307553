package com.lw.apcc.module.iot.service.warning;

import cn.hutool.core.map.MapUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.lw.apcc.common.enums.CommonStatusEnum;
import com.lw.apcc.common.pojo.PageResult;
import com.lw.apcc.common.util.object.BeanUtils;
import com.lw.apcc.common.util.object.ObjectUtil;
import com.lw.apcc.framework.mybatis.core.service.BaseServiceImpl;
import com.lw.apcc.framework.mybatis.core.util.MyBatisUtils;
import com.lw.apcc.module.iot.controller.admin.warning.vo.WarningUserPageReqVO;
import com.lw.apcc.module.iot.controller.admin.warning.vo.WarningUserRespVO;
import com.lw.apcc.module.iot.controller.admin.warning.vo.WarningUserSaveReqVO;
import com.lw.apcc.module.iot.controller.admin.warning.vo.WarningUserSimpleRespVO;
import com.lw.apcc.module.iot.convert.WarningUserConvert;
import com.lw.apcc.module.iot.dal.dataobject.warning.WarningUserDO;
import com.lw.apcc.module.iot.dal.mapper.units.UnitRelMapper;
import com.lw.apcc.module.iot.dal.mapper.warning.WarningUserMapper;
import com.lw.apcc.module.system.api.permission.dto.DataPermissionRespDTO;
import com.lw.apcc.module.system.cache.DataPermissionCache;
import com.lw.apcc.module.system.enums.permission.DataPermissionCodeEnum;
import jakarta.annotation.Resource;
import org.apache.commons.compress.utils.Lists;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Set;

import static com.lw.apcc.common.exception.util.ServiceExceptionUtil.exception;
import static com.lw.apcc.module.iot.enums.ErrorCodeConstants.*;

/**
 * 预警用户信息 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class WarningUserServiceImpl extends BaseServiceImpl<WarningUserMapper, WarningUserDO> implements WarningUserService {

    @Resource
    private UnitRelMapper unitRelMapper;

    @Transactional(rollbackFor = Exception.class)
    @Override
    public Long createWarningUser(WarningUserSaveReqVO createReqVO) {
        // 校验手机号重复
        validateRepetition(WarningUserDO::getPhone, createReqVO.getPhone(), WARNING_USER_PHONE_REPETITION, MapUtil.of(WarningUserDO::getUnitId, createReqVO.getUnitId()));
        // 校验邮箱重复
        validateRepetition(WarningUserDO::getEmail, createReqVO.getEmail(), WARNING_USER_EMAIL_REPETITION, MapUtil.of(WarningUserDO::getUnitId, createReqVO.getUnitId()));

        // 插入
        WarningUserDO warningUser = BeanUtils.toBean(createReqVO, WarningUserDO.class);
        baseMapper.insert(warningUser);
        // 返回
        return warningUser.getId();
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void updateWarningUser(WarningUserSaveReqVO updateReqVO) {
        // 校验手机号重复
        validateRepetition(updateReqVO.getId(), WarningUserDO::getPhone, updateReqVO.getPhone(), WARNING_USER_PHONE_REPETITION, MapUtil.of(WarningUserDO::getUnitId, updateReqVO.getUnitId()));
        // 校验邮箱重复
        validateRepetition(updateReqVO.getId(), WarningUserDO::getEmail, updateReqVO.getEmail(), WARNING_USER_EMAIL_REPETITION, MapUtil.of(WarningUserDO::getUnitId, updateReqVO.getUnitId()));
        // 校验存在
        validateWarningUserExists(updateReqVO.getId());
        // 更新
        WarningUserDO updateObj = BeanUtils.toBean(updateReqVO, WarningUserDO.class);
        baseMapper.updateById(updateObj);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void deleteWarningUser(Long id) {
        // 校验存在
        validateWarningUserExists(id);
        // 删除
        baseMapper.deleteById(id);
    }

    private void validateWarningUserExists(Long id) {
        if (baseMapper.selectById(id) == null) {
            throw exception(WARNING_USER_NOT_EXISTS);
        }
    }

    @Override
    public WarningUserRespVO getWarningUser(Long id) {
        WarningUserDO warningUser = baseMapper.selectById(id);
        if (warningUser == null) {
            return null;
        }
        WarningUserRespVO respVO = WarningUserConvert.INSTANCE.convertVO(warningUser);

        String name = unitRelMapper.getNameById(warningUser.getUnitId());
        respVO.setUnitName(name);
        return respVO;
    }

    @Override
    public PageResult<WarningUserRespVO> getWarningUserPage(WarningUserPageReqVO pageReqVO) {
        DataPermissionRespDTO dataPermission = DataPermissionCache.getDataPermissionByCode(DataPermissionCodeEnum.UNIT);

        IPage<WarningUserRespVO> iPage = baseMapper.getWarningUserPage(MyBatisUtils.buildPage(pageReqVO), pageReqVO, dataPermission);
        return MyBatisUtils.buildPageResult(iPage);
    }

    /**
     * 根据经营主体id查询预警联系人id
     *
     * @param unitId
     * @return
     */
    @Override
    public List<WarningUserSimpleRespVO> listSimpleByUnitId(Long unitId) {
        List<WarningUserDO> list = list(Wrappers.<WarningUserDO>lambdaQuery().eq(WarningUserDO::getUnitId, unitId));
        return WarningUserConvert.INSTANCE.convertSimpleVO(list);
    }

    /**
     * 根据预警联系人id查询预警联系人信息
     *
     * @param userIds
     * @return
     */
    @Override
    public List<WarningUserSimpleRespVO> listSimpleBIds(Set<Long> userIds) {
        if (ObjectUtil.isEmpty(userIds)) {
            return Lists.newArrayList();
        }

        List<WarningUserDO> list = listByIds(userIds);

        return WarningUserConvert.INSTANCE.convertSimpleVO(list);
    }

    /**
     * 更新启用状态 0：禁用 1：启用
     *
     * @param id
     * @param status
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public Boolean updateStatusById(Long id, Integer status) {
        return update(Wrappers.<WarningUserDO>lambdaUpdate().eq(WarningUserDO::getId, id).set(WarningUserDO::getStatus, status).set(WarningUserDO::getUpdateTime, LocalDateTime.now()));
    }

    /**
     * 根据id查询启用用户
     *
     * @param ids
     * @return
     */
    @Override
    public List<WarningUserDO> listEnableByIds(Set<Long> ids) {
        if (ObjectUtil.isEmpty(ids)) {
            return Lists.newArrayList();
        }
        return list(Wrappers.<WarningUserDO>lambdaQuery().eq(WarningUserDO::getStatus, CommonStatusEnum.ENABLE.getStatus()).in(WarningUserDO::getId, ids));
    }

    /**
     * 根据经营主体保存用户
     *
     * @param unitId
     * @param saveList
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public Boolean batchSaveByUnitId(Long unitId, List<WarningUserSaveReqVO> saveList) {
        // 更新
        List<WarningUserDO> userList = BeanUtils.toBean(saveList, WarningUserDO.class);
        userList.forEach(
                user -> {
                    user.setUnitId(unitId);
                }
        );
        return baseMapper.insertOrUpdateBatch(userList);
    }

}
