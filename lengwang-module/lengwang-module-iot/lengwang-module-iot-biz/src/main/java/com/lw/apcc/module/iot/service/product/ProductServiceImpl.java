package com.lw.apcc.module.iot.service.product;

import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.google.common.collect.Maps;
import com.lw.apcc.common.enums.CommonStatusEnum;
import com.lw.apcc.common.pojo.PageResult;
import com.lw.apcc.common.util.collection.CollectionUtils;
import com.lw.apcc.common.util.object.BeanUtils;
import com.lw.apcc.framework.mybatis.core.service.BaseServiceImpl;
import com.lw.apcc.framework.tenant.core.util.TenantUtils;
import com.lw.apcc.module.iot.cache.ProductCache;
import com.lw.apcc.module.iot.cache.ProductTenantCache;
import com.lw.apcc.module.iot.cache.SupplierCache;
import com.lw.apcc.module.iot.controller.admin.product.vo.*;
import com.lw.apcc.module.iot.convert.ProductConvert;
import com.lw.apcc.module.iot.dal.dataobject.product.ProductDO;
import com.lw.apcc.module.iot.dal.mapper.product.ProductMapper;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.Set;

import static com.lw.apcc.common.exception.util.ServiceExceptionUtil.exception;
import static com.lw.apcc.module.iot.enums.ErrorCodeConstants.PRODUCT_CODE_NAME_DUPLICATE;
import static com.lw.apcc.module.iot.enums.ErrorCodeConstants.PRODUCT_NOT_EXISTS;

/**
 * 产品管理 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class ProductServiceImpl extends BaseServiceImpl<ProductMapper, ProductDO> implements ProductService {

    @Resource
    private ProductSensorService productSensorService;

    @Resource
    private ProductWarningSettingService warningSettingService;

    @Transactional(rollbackFor = Exception.class)
    @Override
    public Long createProduct(ProductSaveReqVO createReqVO) {
        // 校验重复
        validateRepetition(ProductDO::getCode, createReqVO.getCode(), PRODUCT_CODE_NAME_DUPLICATE, MapUtil.of(ProductDO::getName, createReqVO.getName()));

        // 插入
        ProductDO product = BeanUtils.toBean(createReqVO, ProductDO.class);
        baseMapper.insert(product);

        // 保存传感器
        productSensorService.saveBatchByProduct(createReqVO.getSensorList(), createReqVO.getId());
        // 清楚缓存
        ProductCache.clear();
        // 返回
        return product.getId();
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void updateProduct(ProductSaveReqVO updateReqVO) {
        // 校验重复
        validateRepetition(updateReqVO.getId(), ProductDO::getCode, updateReqVO.getCode(), PRODUCT_CODE_NAME_DUPLICATE, MapUtil.of(ProductDO::getName, updateReqVO.getName()));
        // 校验存在
        validateProductExists(updateReqVO.getId());
        // 更新
        ProductDO updateObj = BeanUtils.toBean(updateReqVO, ProductDO.class);
        baseMapper.updateById(updateObj);
        // 保存传感器
        productSensorService.saveBatchByProduct(updateReqVO.getSensorList(), updateReqVO.getId());
        // 清楚缓存
        ProductCache.clear();
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void deleteProduct(Long id) {
        // 校验存在
        validateProductExists(id);
        // 删除
        baseMapper.deleteById(id);
        // 清楚缓存
        ProductCache.clear();
    }

    private void validateProductExists(Long id) {
        if (baseMapper.selectById(id) == null) {
            throw exception(PRODUCT_NOT_EXISTS);
        }
    }

    @Override
    public ProductDO getProduct(Long id) {
        return baseMapper.selectById(id);
    }

    /**
     * 产品分页管理
     *
     * @param pageReqVO
     * @return
     */
    @Override
    public PageResult<ProductRespVO> pageByProduct(ProductPageReqVO pageReqVO) {
        PageResult<ProductDO> pageResult = baseMapper.selectPage(pageReqVO);

        List<ProductDO> productList = pageResult.getList();

        List<ProductRespVO> productRespList = CollectionUtils.toListTrim(
                productList,
                product -> {
                    ProductRespVO respVO = ProductConvert.INSTANCE.convertVO(product);
                    if (respVO != null) {
                        respVO.setSupplierName(SupplierCache.getNameById(respVO.getSupplierId()));
                    }
                    return respVO;
                }
        );

        return new PageResult(productRespList, pageResult.getTotal());
    }

    /**
     * 产品精简列表
     *
     * @return
     */
    @Override
    public List<ProductSimpleRespVO> listSimple() {
        List<ProductDO> list = baseMapper.selectList(Wrappers.<ProductDO>lambdaQuery().eq(ProductDO::getShowStatus, CommonStatusEnum.ENABLE.getStatus()).in(ProductDO::getId, ProductTenantCache.getProductIds()));
        return ProductConvert.INSTANCE.convertSimpleVO(list);
    }

    /**
     * 产品精简列表 可筛选节点类型
     *
     * @return
     */
    @Override
    public List<ProductSimpleRespVO> listSimpleByNodeType(String nodeType) {

        List<ProductDO> list;

        if (ObjectUtil.isNotEmpty(nodeType)) {
            list = baseMapper.selectList(Wrappers.lambdaQuery(ProductDO.class).eq(ProductDO::getNodeType, nodeType).eq(ProductDO::getShowStatus, CommonStatusEnum.ENABLE.getStatus()));
        } else {
            list = baseMapper.selectList(ProductDO::getShowStatus, CommonStatusEnum.ENABLE.getStatus());
        }

        return ProductConvert.INSTANCE.convertSimpleVO(list);
    }

    @Override
    public List<ProductSimpleRespVO> listProductByCodes(Set<String> productCodes) {

        List<ProductDO> list = baseMapper.selectList(Wrappers.lambdaQuery(ProductDO.class).in(ProductDO::getCode, productCodes).eq(ProductDO::getShowStatus, CommonStatusEnum.ENABLE.getStatus()));

        return ProductConvert.INSTANCE.convertSimpleVO(list);
    }

    /**
     * 根据产品名称和编码查询产品信息
     *
     * @param name
     * @param code
     * @return
     */
    @Override
    public ProductDO getByNameAndCode(String name, String code) {
        List<ProductDO> list = list(Wrappers.<ProductDO>lambdaQuery().eq(ProductDO::getName, name).eq(ProductDO::getCode, code));
        if (ObjectUtil.isEmpty(list)) {
            return null;
        }
        return list.get(0);
    }

    /**
     * 修改预警状态（1正常 0停用）
     *
     * @param warningStatusReqVO
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public Boolean updateWarningStatus(ProductWarningStatusReqVO warningStatusReqVO) {
        super.update(Wrappers.<ProductDO>lambdaUpdate().eq(ProductDO::getId, warningStatusReqVO.getId()).set(ProductDO::getWarningStatus, warningStatusReqVO.getWarningStatus()).set(ProductDO::getUpdateTime, LocalDateTime.now()));

        // 清楚缓存
        ProductCache.clear();
        return true;
    }

    /**
     * 查询预警详情
     *
     * @param id
     * @return
     */
    @Override
    public ProductWarningDetailsRespVO getProductWarningDetails(Long id) {
        ProductDO product = baseMapper.selectById(id);
        if (product == null) {
            throw exception(PRODUCT_NOT_EXISTS);
        }

        // 产品预警详情
        ProductWarningDetailsRespVO detailsResp = new ProductWarningDetailsRespVO();
        // 基础值
        detailsResp.setId(product.getId());
        detailsResp.setWarningStatus(ObjectUtil.defaultIfNull(product.getWarningStatus(), CommonStatusEnum.ENABLE.getStatus()));

        // 通用预警设置
        List<ProductWarningGeneralSettingRespVO> generalSettingList = warningSettingService.listGeneralSetting(product.getId());
        detailsResp.setGeneralSettingList(generalSettingList);

        return detailsResp;
    }

    /**
     * @description 根据设备分组查询产品信息
     * <AUTHOR> yang
     * @date 2025/4/9 17:34
     */
    @Override
    public Map<String, List<ProductRespVO>> listByProductGroup() {
        List<ProductDO> list = listShowEnable();
        if (ObjectUtil.isEmpty(list)) {
            return Maps.newHashMap();
        }
        List<ProductRespVO> productList = ProductConvert.INSTANCE.convertVO(list);
        return CollectionUtils.groupingBy(productList, ProductRespVO::getProductGroup);
    }

    /**
     * 获取产品列表
     *
     * @return
     */
    private List<ProductDO> listShowEnable() {
        LambdaQueryWrapper<ProductDO> queryWrapper = Wrappers.<ProductDO>lambdaQuery()
                .eq(ProductDO::getShowStatus, CommonStatusEnum.ENABLE.getStatus())
                .in(ProductDO::getId, ProductTenantCache.getProductIds())
                .orderByAsc(ProductDO::getSort);

        return baseMapper.selectList(queryWrapper);
    }


    /**
     * 产品全部精简列表
     *
     * @return
     */
    @Override
    public List<ProductSimpleRespVO> listAll() {
        LambdaQueryWrapper<ProductDO> queryWrapper = Wrappers.<ProductDO>lambdaQuery().eq(ProductDO::getShowStatus, CommonStatusEnum.ENABLE.getStatus()).orderByAsc(ProductDO::getSort);
        if (!TenantUtils.isSystemTenant()) {
            queryWrapper.in(ProductDO::getId, ProductTenantCache.getProductIds());
        }

        List<ProductDO> list = baseMapper.selectList(queryWrapper);
        return ProductConvert.INSTANCE.convertSimpleVO(list);
    }


}
