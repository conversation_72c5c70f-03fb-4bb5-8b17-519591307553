package com.lw.apcc.module.iot.service.product;

import com.lw.apcc.common.pojo.PageResult;
import com.lw.apcc.framework.mybatis.core.service.BaseService;
import com.lw.apcc.module.iot.controller.admin.product.vo.*;
import com.lw.apcc.module.iot.dal.dataobject.product.ProductDO;
import jakarta.validation.Valid;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 产品管理 Service 接口
 *
 * <AUTHOR>
 */
public interface ProductService extends BaseService<ProductDO> {

    /**
     * 创建产品管理
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createProduct(@Valid ProductSaveReqVO createReqVO);

    /**
     * 更新产品管理
     *
     * @param updateReqVO 更新信息
     */
    void updateProduct(@Valid ProductSaveReqVO updateReqVO);

    /**
     * 删除产品管理
     *
     * @param id 编号
     */
    void deleteProduct(Long id);

    /**
     * 获得产品管理
     *
     * @param id 编号
     * @return 产品管理
     */
    ProductDO getProduct(Long id);

    /**
     * 产品分页管理
     *
     * @param pageReqVO
     * @return
     */
    PageResult<ProductRespVO> pageByProduct(ProductPageReqVO pageReqVO);

    /**
     * 产品精简列表
     *
     * @return
     */
    List<ProductSimpleRespVO> listSimple();

    /**
     * 产品精简列表 可筛选节点类型
     *
     * @return
     */
    List<ProductSimpleRespVO> listSimpleByNodeType(String nodeType);

    /**
     * 根据产品编码查询产品信息
     *
     * @param productCodes
     * @return
     */
    List<ProductSimpleRespVO> listProductByCodes(Set<String> productCodes);

    /**
     * 根据产品名称和编码查询产品信息
     *
     * @param name
     * @param code
     * @return
     */
    ProductDO getByNameAndCode(String name, String code);

    /**
     * 修改预警状态（1正常 0停用）
     *
     * @param warningStatusReqVO
     * @return
     */
    Boolean updateWarningStatus(ProductWarningStatusReqVO warningStatusReqVO);

    /**
     * 查询预警详情
     *
     * @param id
     * @return
     */
    ProductWarningDetailsRespVO getProductWarningDetails(Long id);

    /**
     * @description 根据设备分组查询产品信息
     * <AUTHOR> yang
     * @date 2025/4/9 17:34
     */
    Map<String, List<ProductRespVO>> listByProductGroup();

    /**
     * 产品全部精简列表
     *
     * @return
     */
    List<ProductSimpleRespVO> listAll();


}
