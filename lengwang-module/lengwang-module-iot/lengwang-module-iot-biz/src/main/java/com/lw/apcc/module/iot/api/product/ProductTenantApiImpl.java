package com.lw.apcc.module.iot.api.product;

import com.lw.apcc.common.pojo.CommonResult;
import com.lw.apcc.module.iot.service.product.ProductTenantService;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RestController;

import java.util.Set;

import static com.lw.apcc.common.pojo.CommonResult.success;


/**
 * 物联网产品 API 接口
 *
 * <AUTHOR>
 */
@RestController // 提供 RESTful API 接口，给 Feign 调用
@Validated
public class ProductTenantApiImpl implements ProductTenantApi {

    @Resource
    private ProductTenantService productTenantService;

    @Override
    public CommonResult<Set<Long>> getProductIdsByTenantId(Long tenantId) {
        return success(productTenantService.getProductIdsByTenantId(tenantId));
    }

}
