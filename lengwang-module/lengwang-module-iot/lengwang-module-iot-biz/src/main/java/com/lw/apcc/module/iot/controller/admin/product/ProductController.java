package com.lw.apcc.module.iot.controller.admin.product;

import com.lw.apcc.common.pojo.CommonResult;
import com.lw.apcc.common.pojo.PageParam;
import com.lw.apcc.common.pojo.PageResult;
import com.lw.apcc.common.util.object.BeanUtils;
import com.lw.apcc.framework.excel.core.util.ExcelUtils;
import com.lw.apcc.framework.operatelog.core.annotations.OperateLog;
import com.lw.apcc.module.iot.controller.admin.product.vo.*;
import com.lw.apcc.module.iot.dal.dataobject.product.ProductDO;
import com.lw.apcc.module.iot.service.product.ProductService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

import static com.lw.apcc.common.pojo.CommonResult.success;
import static com.lw.apcc.framework.operatelog.core.enums.OperateTypeEnum.EXPORT;

@Tag(name = "管理后台 - 产品管理")
@RestController
@RequestMapping("/iot/product")
@Validated
public class ProductController {

    @Resource
    private ProductService productService;

    @PostMapping("/create")
    @Operation(summary = "创建产品管理")
    @PreAuthorize("@ss.hasPermission('iot:product:create')")
    public CommonResult<Long> createProduct(@Valid @RequestBody ProductSaveReqVO createReqVO) {
        return success(productService.createProduct(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新产品管理")
    @PreAuthorize("@ss.hasPermission('iot:product:update')")
    public CommonResult<Boolean> updateProduct(@Valid @RequestBody ProductSaveReqVO updateReqVO) {
        productService.updateProduct(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除产品管理")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('iot:product:delete')")
    public CommonResult<Boolean> deleteProduct(@RequestParam("id") Long id) {
        productService.deleteProduct(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得产品管理")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('iot:product:query')")
    public CommonResult<ProductRespVO> getProduct(@RequestParam("id") Long id) {
        ProductDO product = productService.getProduct(id);
        return success(BeanUtils.toBean(product, ProductRespVO.class));
    }

    @GetMapping("/page")
    @Operation(summary = "获得产品管理分页")
    @PreAuthorize("@ss.hasPermission('iot:product:query')")
    public CommonResult<PageResult<ProductRespVO>> pageByProduct(@Valid ProductPageReqVO pageReqVO) {
        PageResult<ProductRespVO> pageResult = productService.pageByProduct(pageReqVO);
        return success(pageResult);
    }

    @GetMapping("/list-all")
    @Operation(summary = "产品全部精简列表")
    public CommonResult<List<ProductSimpleRespVO>> listAll() {
        return success(productService.listAll());
    }

    @GetMapping("/list-simple")
    @Operation(summary = "产品精简列表")
    public CommonResult<List<ProductSimpleRespVO>> listSimple() {
        return success(productService.listSimple());
    }

    @GetMapping("/list-simple-by-node-type")
    @Operation(summary = "产品精简列表,可筛选节点类型")
    public CommonResult<List<ProductSimpleRespVO>> listSimpleByNodeType(@RequestParam("nodeType") String nodeType) {
        return success(productService.listSimpleByNodeType(nodeType));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出产品管理 Excel")
    @PreAuthorize("@ss.hasPermission('iot:product:export')")
    @OperateLog(type = EXPORT)
    public void exportProductExcel(@Valid ProductPageReqVO pageReqVO, HttpServletResponse response) {
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        List<ProductRespVO> list = productService.pageByProduct(pageReqVO).getList();
        // 导出 Excel
        ExcelUtils.write(response, "产品列表", "数据", ProductRespVO.class, list);
    }

    @GetMapping("/get-product-warning-details")
    @Operation(summary = "查询预警详情")
    public CommonResult<ProductWarningDetailsRespVO> getProductWarningDetails(@RequestParam("id") Long id) {
        return success(productService.getProductWarningDetails(id));
    }

    @PostMapping("/update-warning-status")
    @Operation(summary = "修改预警状态（1正常 0停用）")
    public CommonResult<Boolean> updateWarningStatus(@RequestBody ProductWarningStatusReqVO warningStatusReqVO) {
        return success(productService.updateWarningStatus(warningStatusReqVO));
    }

    @GetMapping("/list-by-product-group")
    @Operation(summary = "根据设备分组查询产品信息")
    public CommonResult<Map<String, List<ProductRespVO>>> listByProductGroup() {
        return success(productService.listByProductGroup());
    }

}
