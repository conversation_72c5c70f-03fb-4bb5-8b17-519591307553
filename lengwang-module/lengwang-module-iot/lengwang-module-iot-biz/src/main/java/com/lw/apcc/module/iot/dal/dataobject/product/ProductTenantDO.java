package com.lw.apcc.module.iot.dal.dataobject.product;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 产品租户关联信息 DO
 *
 * <AUTHOR>
 */
@TableName("iot_product_tenant")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ProductTenantDO {

    /**
     * 主键ID
     */
    @TableId(type = IdType.ASSIGN_ID)
    private Long id;
    /**
     * 产品ID
     */
    private Long productId;
    /**
     * 租户ID
     */
    private Long tenantId;

}
