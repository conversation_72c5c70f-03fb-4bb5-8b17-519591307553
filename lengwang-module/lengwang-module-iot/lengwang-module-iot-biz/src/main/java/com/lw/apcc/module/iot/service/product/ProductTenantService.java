package com.lw.apcc.module.iot.service.product;

import com.lw.apcc.framework.mybatis.core.service.BaseService;
import com.lw.apcc.module.iot.controller.admin.product.vo.ProductTenantSaveReqVO;
import com.lw.apcc.module.iot.dal.dataobject.product.ProductTenantDO;

import java.util.Set;

/**
 * 产品租户关联信息 Service 接口
 *
 * <AUTHOR>
 */
public interface ProductTenantService extends BaseService<ProductTenantDO> {

    /**
     * 根据租户ID查询产品ID
     *
     * @param tenantId
     * @return
     */
    Set<Long> getProductIdsByTenantId(Long tenantId);

    /**
     * 根据租户id批量保存租户id
     *
     * @param saveReqVO
     * @return
     */
    Boolean saveByTenantId(ProductTenantSaveReqVO saveReqVO);

}
