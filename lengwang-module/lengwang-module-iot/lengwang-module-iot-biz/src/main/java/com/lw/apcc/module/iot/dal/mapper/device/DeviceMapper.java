package com.lw.apcc.module.iot.dal.mapper.device;

import com.baomidou.mybatisplus.annotation.InterceptorIgnore;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.lw.apcc.common.pojo.PageResult;
import com.lw.apcc.common.util.object.ObjectUtil;
import com.lw.apcc.common.util.string.StrUtils;
import com.lw.apcc.common.constants.IdConstants;
import com.lw.apcc.framework.mybatis.core.mapper.BaseMapperX;
import com.lw.apcc.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.lw.apcc.module.iot.api.device.dto.*;
import com.lw.apcc.module.iot.controller.admin.device.vo.*;
import com.lw.apcc.module.iot.dal.dataobject.device.DeviceDO;
import com.lw.apcc.module.iot.service.device.dto.DeviceProductRespDTO;
import com.lw.apcc.module.system.api.permission.dto.DataPermissionRespDTO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDateTime;
import java.util.LinkedList;
import java.util.List;
import java.util.Set;

/**
 * 设备 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface DeviceMapper extends BaseMapperX<DeviceDO> {

    default PageResult<DeviceDO> selectPage(DevicePageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<DeviceDO>().likeIfPresent(DeviceDO::getName, reqVO.getName()).eqIfPresent(DeviceDO::getProductId, reqVO.getProductId()).eqIfPresent(DeviceDO::getCode, reqVO.getCode()).orderByDesc(DeviceDO::getCreateTime));
    }

    /**
     * 根据设备查询
     *
     * @param page
     * @param pageReqVO
     * @return
     */
    IPage<DeviceRespVO> pageByDevice(Page page, @Param("pageReqVO") DevicePageReqVO pageReqVO, @Param("dataPermission") DataPermissionRespDTO dataPermission);

    /**
     * 根据设备查询列表
     *
     * @param pageReqVO
     * @param dataPermission
     * @return
     */
    List<DeviceRespVO> listByDevice(@Param("pageReqVO") DevicePageReqVO pageReqVO, @Param("dataPermission") DataPermissionRespDTO dataPermission);

    /**
     * 精简列表
     *
     * @param simpleReqVO
     * @return
     */
    List<DeviceSimpleRespVO> listBySimple(DeviceSimpleReqVO simpleReqVO);

    /**
     * 根据供应商查询设备信息
     *
     * @param supplier
     * @param productCodes odes
     * @return
     */
    List<DeviceProductRespDTO> listDeviceBySupplier(@Param("supplier") String supplier, @Param("productCodes") Set<String> productCodes);

    /**
     * 根据资产id查询设备详情
     *
     * @param assetId
     * @param assetType
     * @return
     */
    List<DeviceDetailsRespVO> listDetailsByAssetId(@Param("assetId") Long assetId, @Param("assetType") String assetType, @Param("productType") String productType, @Param("productCode") String productCode);

    /**
     * 根据id查询设备详情
     *
     * @param id
     * @return
     */
    DeviceDetailsRespVO getDetailsById(@Param("id") Long id);

    /**
     * 查询单位的物联网设备数量信息
     *
     * @param assetIds 单位Id集合
     * @return 物联网设备数量信息
     */
    List<IotUnitDevicesCountRespDTO> getAssetDeviceTotal(@Param("assetIds") Set<Long> assetIds);

    /**
     * 根据单位Id查询单位总的各类物联网设备数量
     *
     * @param unitIds 单位ID集合
     * @return 物联网设备数量信息
     */
    @InterceptorIgnore(tenantLine = "true")
    List<IotUnitDevicesCountRespDTO> getUnitDeviceTotal(Set<Long> unitIds);

    /**
     * 根据资产id删除设备
     *
     * @param assetId
     * @return
     */
    boolean removeByAssetId(@Param("assetId") Long assetId);

    /**
     * 根据设备编码查询设备
     *
     * @param code
     * @return
     */
    DeviceSimpleRespVO getSimpleByCode(@Param("code") String code);

    /**
     * 根据id查询设备详情
     *
     * @param id
     * @return
     */
    DeviceSimpleRespVO getSimpleById(@Param("id") Long id);

    /**
     * 根据单位id查询设备详情
     *
     * @param unitIds
     * @return
     */
    List<DeviceDetailsRespDTO> getDetailsByUnitId(@Param("unitIds") Set<Long> unitIds, @Param("assetType") String assetType);

    /**
     * 查询某地区的每类监测设备有多少个
     *
     * @param areaPath 地区路径
     * @return 监测设备数量
     */
    List<IotUnitAreaDevicesCountRespDTO> getAreaDeviceCount(@Param("areaPath") String areaPath);

    /**
     * 获取某地区某段时间内的预警（所有种类的预警）数量变化
     *
     * @param areaPath  地区路径
     * @param startDate 开始时间
     * @return 预警数量变化
     */
    List<IotUnitAreaWarningCountRespDTO> getAreaWarningCount(@Param("areaPath") String areaPath, @Param("startDate") String startDate);

    /**
     * 获取单位的设备数据，用于冷链大屏
     *
     * @param unitId
     * @param assetType
     * @return
     */
    LinkedList<UnitAssetDeviceDataRespDTO> getUnitIotData(@Param("unitId") Long unitId, @Param("assetType") String assetType);

    /**
     * 查询单位是否有物联网设备数据，通过设备的标记位has_history判断
     *
     * @param unitIds
     * @return
     */
    List<UnitIotDataRespDTO> getUnitHasHistory(@Param("unitIds") Set<Long> unitIds);

    /**
     * 获取冷藏车(及移动冷库)实时位置（供冷链大屏展示冷藏车点位）
     *
     * @param areaPath
     * @param assetType
     * @return
     */
    List<TruckRealtimeLocationRespDTO> getTruckRealtimeLocation(@Param("areaPath") String areaPath, @Param("assetType") String assetType);

    /**
     * 根据id查询子节点id集合
     *
     * @param path
     * @return
     */
    Set<Long> listChildIdsByPath(@Param("path") String path);

    /**
     * DTU控制箱分页列表
     *
     * @param page
     * @param pageReqVO
     * @return
     */
    IPage<DeviceDtuPageRespVO> pageByDtu(Page page, @Param("pageReqVO") DeviceDtuPageReqVO pageReqVO, @Param("dataPermission") DataPermissionRespDTO dataPermission);

    /**
     * 根据父类DT电箱Id查询设备DTU节点
     *
     * @param parentIds
     * @return
     */
    List<DeviceDtuNodePageRespVO> listDtuNodeByParentIds(@Param("parentIds") Set<Long> parentIds);


    /**
     * 根据父类DTU电箱Id查询设备DTU节点
     *
     * @param parentId
     * @return
     */
    List<DeviceDtuNodeRespVO> listDtuNodeByParentId(@Param("parentId") Long parentId);

    /**
     * 根据节点id查询节点实时数据
     *
     * @param parentIds
     * @param nodeType
     * @return
     */
    LinkedList<DeviceDtuNodeRealtimeRespVO> listDtuNodeRealtimeByNodeType(@Param("parentIds") Set<Long> parentIds, @Param("nodeType") String nodeType);

    /**
     * 根据DTU设备Id查询设备详情
     *
     * @param dtuDeviceId
     * @param productCodes
     * @return
     */
    List<DeviceDetailsRespVO> listDetailsByDtuDeviceId(@Param("dtuDeviceId") Long dtuDeviceId, @Param("productCodes") Set<String> productCodes);

    /**
     * 根据设备Id查询DTU节点信息
     *
     * @param deviceId
     * @return
     */
    DeviceDtuNodeRespVO getDtuNodeById(@Param("deviceId") Long deviceId);

    /**
     * 查询各类设备数量，可使用供应商筛选
     *
     * @return
     */
    @InterceptorIgnore(tenantLine = "true")
    List<DeviceNodeTypeStatisticsRespVO> getDeviceNodeTypeStatistics(@Param("reqVO") DeviceTotalStatisticsReqVO reqVO, @Param("dataPermission") DataPermissionRespDTO dataPermission);

    /**
     * @description 根据资产id查询设备详情
     * <AUTHOR> yang
     * @date 2024/6/14 17:41
     */
    DeviceSimpleRespVO getSimpleBySensorSlug(@Param("assetId") Long assetId, @Param("sensorSlug") String sensorSlug, @Param("sensorSlugs") String... sensorSlugs);

    /**
     * @description 根据资产id查询设备详情
     * <AUTHOR> yang
     * @date 2024/6/14 17:41
     */
    List<DeviceSimpleRespVO> listSimpleBySensorSlug(@Param("assetId") Long assetId, @Param("sensorSlug") String sensorSlug, @Param("sensorSlugs") String... sensorSlugs);

    /**
     * 根据资产id查询DTU系统开关
     *
     * @param assetId
     * @return
     */
    DeviceSimpleRespVO getDtuSystemSwitchByAssetId(@Param("assetId") Long assetId);

    /**
     * 根据设备编码查询重复绑定的设备
     *
     * @param codes
     * @return
     */
    @InterceptorIgnore(tenantLine = "true")
    List<String> listDuplicateBindingByCodes(@Param("codes") Set<String> codes, @Param("dataPermission") DataPermissionRespDTO dataPermission);

    /**
     * 根据设备编码查询设备
     *
     * @param codes
     * @return
     */
    @InterceptorIgnore(tenantLine = "true")
    List<DeviceDO> listByCodes(@Param("codes") Set<String> codes, @Param("dataPermission") DataPermissionRespDTO dataPermission);

    /**
     * 查询单位绑定了设备的冷库ID集合
     *
     * @param unitId
     * @return
     */
    Set<Long> getUnitBatchAssetIds(Long unitId, String assetType);

    /**
     * 查询单位的预警次数
     *
     * @param unitId
     * @param warningField
     * @param startDate
     * @param endDate
     * @return
     */
    List<IotUnitAreaWarningCountRespDTO> getUnitWarningCount(Long unitId, String warningField, String startDate, String endDate);

    /**
     * 根据资产id查询设备资产数量
     *
     * @param assetId
     * @return
     */
    List<AssetDeviceCountRespVO> listAssetDeviceCount(@Param("assetId") Long assetId);

    /**
     * 查询设备的监测数据及预警信息
     *
     * @param deviceIds
     * @return
     */
    List<DeviceMonitorRespVO> listDeviceMonitorData(@Param("deviceIds") Set<Long> deviceIds);

    /**
     * 根据资产id
     *
     * @param assetId
     * @param productIds
     * @return
     */
    default List<DeviceDO> listByAssetId(Long assetId, Set<Long> productIds) {
        return selectList(new LambdaQueryWrapperX<DeviceDO>().eq(DeviceDO::getAssetId, assetId).inIfPresent(DeviceDO::getProductId, productIds));
    }

    /**
     * 根据资产id集合查询设备
     *
     * @param assetIds
     * @param productId
     * @return
     */
    default List<DeviceDO> listByAssetIds(Set<Long> assetIds, Long productId) {
        return selectList(new LambdaQueryWrapperX<DeviceDO>().in(DeviceDO::getAssetId, assetIds).eq(DeviceDO::getProductId, productId));
    }

    /**
     * 根据资产id集合查询设备
     *
     * @param assetIds
     * @return
     */
    default List<DeviceDO> listByAssetIds(Set<Long> assetIds) {
        return selectList(new LambdaQueryWrapperX<DeviceDO>().in(DeviceDO::getAssetId, assetIds));
    }

    /**
     * 获取设备的电量和信号高低水平
     *
     * @param reqVO
     * @return
     */
    List<DeviceVoltRssiLevelRespVO> getDeviceVoltRssiLevel(@Param("reqVO") DeviceVoltRssiLevelReqVO reqVO);

    /**
     * 根据资产id集合查询设备
     *
     * @param assetId
     * @return
     */
    default List<DeviceDO> listByAssetId(Long assetId, String name) {
        LambdaQueryWrapper<DeviceDO> queryWrapper = new LambdaQueryWrapperX<DeviceDO>()
                .in(DeviceDO::getAssetId, assetId)
                .eq(DeviceDO::getParentId, IdConstants.PARENT_ID_ZERO);
        if (StrUtils.isNotEmpty(name)) {
            queryWrapper.and(
                    eq ->
                            eq.like(DeviceDO::getName, name).or(
                                    eqi -> eqi.like(DeviceDO::getCode, name)
                            )
            );
        }
        return selectList(queryWrapper);
    }

    /**
     * 根据资产id和设备编码查询设备详情
     *
     * @param assetId
     * @param code
     * @return
     */
    default DeviceDO getByCode(Long assetId, String code) {
        List<DeviceDO> deviceList = selectList(new LambdaQueryWrapperX<DeviceDO>().eq(DeviceDO::getAssetId, assetId).eq(DeviceDO::getCode, code));
        if (ObjectUtil.isNotEmpty(deviceList)) {
            return deviceList.get(0);
        }
        return null;
    }

    /**
     * 单位信息
     *
     * @param startTime
     * @param endTime
     * @return
     */
    default List<DeviceDO> listDevice(LocalDateTime startTime, LocalDateTime endTime) {
        LambdaQueryWrapperX<DeviceDO> wrapper = new LambdaQueryWrapperX<DeviceDO>()
                .orderByDesc(DeviceDO::getUpdateTime);

        if (ObjectUtil.isNotEmpty(startTime) && ObjectUtil.isNotEmpty(endTime)) {
            wrapper.between(DeviceDO::getUpdateTime, startTime, endTime);
        }

        return selectList(wrapper);
    }

    /*
     * 根据单位id查询设备数量
     *
     * @param unitId
     * @return
     */
    default List<DeviceDO> getDeviceCountByUnitId(Long unitId) {
        List<DeviceDO> deviceList = selectList(new LambdaQueryWrapperX<DeviceDO>()
                .eq(DeviceDO::getUnitId, unitId)
                // 去掉禁用状态的设备
                .ne(DeviceDO::getStatus, 0)
        );
        return deviceList;
    }

}
