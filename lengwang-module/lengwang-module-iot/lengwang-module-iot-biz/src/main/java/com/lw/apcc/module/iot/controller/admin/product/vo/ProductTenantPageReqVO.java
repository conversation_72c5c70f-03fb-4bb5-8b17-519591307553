package com.lw.apcc.module.iot.controller.admin.product.vo;

import lombok.*;
import java.util.*;
import io.swagger.v3.oas.annotations.media.Schema;
import com.lw.apcc.common.pojo.PageParam;

@Schema(description = "管理后台 - 产品租户关联信息分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class ProductTenantPageReqVO extends PageParam {

    @Schema(description = "产品ID", example = "27307")
    private Long productId;

}