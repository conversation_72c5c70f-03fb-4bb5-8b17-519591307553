package com.lw.apcc.module.iot.controller.admin.product.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.util.Set;

@Schema(description = "管理后台 - 产品租户关联信息新增 Request VO")
@Data
public class ProductTenantSaveReqVO {

    @Schema(description = "租户ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "6067")
    @NotNull(message = "租户必传")
    private Long tenantId;

    @Schema(description = "产品ID集合", example = "27307")
    private Set<Long> productIds;

}
