package com.lw.apcc.module.iot.service.device;

import com.lw.apcc.module.iot.data.util.ColdyunIotDeviceUtil;
import com.lw.apcc.module.iot.data.vo.device.coldyunIot.device.DeviceHistoryDataReqVO;
import com.lw.apcc.module.iot.data.vo.device.coldyunIot.device.realtime.DeviceRealtimeDataRespVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import java.util.List;
import java.util.Map;

/**
 * 憨云物联网设备 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
@Slf4j
public class AppColdyunIotDeviceServiceImpl implements AppColdyunIotDeviceService {

    @Override
    public DeviceRealtimeDataRespVO getDeviceRealtimeData(String deviceId) {
        return ColdyunIotDeviceUtil.getDeviceRealtimeData(deviceId);
    }

    @Override
    public List<Map> listDeviceHistoryData(DeviceHistoryDataReqVO reqVO) {
        return ColdyunIotDeviceUtil.listDeviceHistoryData(reqVO);
    }

    @Override
    public List<Map> listDeviceHistoryLog(String deviceId) {
        return ColdyunIotDeviceUtil.listDeviceHistoryLog(deviceId);
    }

} 