package com.lw.apcc.module.iot.service.product;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.google.common.collect.Sets;
import com.lw.apcc.common.util.collection.CollectionUtils;
import com.lw.apcc.common.util.object.ObjectUtil;
import com.lw.apcc.framework.mybatis.core.service.BaseServiceImpl;
import com.lw.apcc.module.iot.cache.ProductTenantCache;
import com.lw.apcc.module.iot.controller.admin.product.vo.ProductTenantSaveReqVO;
import com.lw.apcc.module.iot.dal.dataobject.product.ProductTenantDO;
import com.lw.apcc.module.iot.dal.mapper.product.ProductTenantMapper;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

import java.util.Collection;
import java.util.List;
import java.util.Set;

/**
 * 产品租户关联信息 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class ProductTenantServiceImpl extends BaseServiceImpl<ProductTenantMapper, ProductTenantDO> implements ProductTenantService {

    /**
     * 根据租户ID查询产品ID
     *
     * @param tenantId
     * @return
     */
    @Override
    public Set<Long> getProductIdsByTenantId(Long tenantId) {
        List<ProductTenantDO> list = list(Wrappers.<ProductTenantDO>lambdaQuery().eq(ProductTenantDO::getTenantId, tenantId));
        if (ObjectUtil.isEmpty(list)) {
            return Sets.newHashSet();
        }

        return CollectionUtils.convertSet(list, ProductTenantDO::getProductId);
    }

    /**
     * 根据租户id批量保存租户id
     *
     * @param saveReqVO
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public Boolean saveByTenantId(ProductTenantSaveReqVO saveReqVO) {
        Set<Long> oldProductIds = getProductIdsByTenantId(saveReqVO.getTenantId());

        // 删除
        if (ObjectUtil.isNotEmpty(oldProductIds)) {
            Collection<Long> deleteProductIds = CollUtil.subtract(oldProductIds, saveReqVO.getProductIds());
            baseMapper.removeByTenantId(saveReqVO.getTenantId(), deleteProductIds);
        }

        // 新增
        Collection<Long> createProductIds = CollUtil.subtract(saveReqVO.getProductIds(), oldProductIds);
        if (CollUtil.isNotEmpty(createProductIds)) {
            List<ProductTenantDO> productTenantList = CollectionUtils.convertList(
                    createProductIds,
                    productId -> {
                        ProductTenantDO productTenant = new ProductTenantDO();
                        productTenant.setTenantId(saveReqVO.getTenantId());
                        productTenant.setProductId(productId);
                        return productTenant;
                    }
            );
            baseMapper.insertBatch(productTenantList);
        }
        // 清楚缓存
        ProductTenantCache.clear(saveReqVO.getTenantId());
        return true;
    }

}
