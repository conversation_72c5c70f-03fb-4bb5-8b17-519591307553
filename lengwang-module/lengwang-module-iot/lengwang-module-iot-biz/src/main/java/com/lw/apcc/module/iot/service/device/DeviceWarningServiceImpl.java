package com.lw.apcc.module.iot.service.device;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.lw.apcc.common.enums.CommonStatusEnum;
import com.lw.apcc.common.pojo.PageResult;
import com.lw.apcc.common.security.SecurityUtils;
import com.lw.apcc.common.util.collection.CollectionUtils;
import com.lw.apcc.common.util.object.ObjectUtil;
import com.lw.apcc.common.util.string.StringUtil;
import com.lw.apcc.framework.dict.core.cache.DictCaChe;
import com.lw.apcc.framework.mybatis.core.util.MyBatisUtils;
import com.lw.apcc.module.iot.api.sensor.dto.SensorRespDTO;
import com.lw.apcc.module.iot.api.warning.dto.WarningTypeRespDTO;
import com.lw.apcc.module.iot.cache.ProductCache;
import com.lw.apcc.module.iot.cache.SensorCache;
import com.lw.apcc.module.iot.cache.WarningTypeCache;
import com.lw.apcc.module.iot.controller.admin.device.vo.*;
import com.lw.apcc.module.iot.controller.app.device.vo.AppAssetProductRespVO;
import com.lw.apcc.module.iot.dal.dataobject.device.DeviceDO;
import com.lw.apcc.module.iot.dal.dataobject.product.ProductWarningSettingDO;
import com.lw.apcc.module.iot.dal.dataobject.warning.WarningDO;
import com.lw.apcc.module.iot.dal.dataobject.warning.WarningGlobalSettingDO;
import com.lw.apcc.module.iot.dal.mapper.asset.AssetRelMapper;
import com.lw.apcc.module.iot.dal.mapper.device.DeviceMapper;
import com.lw.apcc.module.iot.dal.mapper.device.DeviceWarningMapper;
import com.lw.apcc.module.iot.enums.DictTypeConstants;
import com.lw.apcc.module.iot.enums.ProductWarningSettingConstants;
import com.lw.apcc.module.iot.enums.WarningRangeTypeEnum;
import com.lw.apcc.module.iot.enums.warning.WarningTypeConstants;
import com.lw.apcc.module.iot.service.asset.dto.AssetRelDetailsRespDTO;
import com.lw.apcc.module.iot.service.product.ProductWarningSettingService;
import com.lw.apcc.module.iot.service.warning.WarningGlobalSettingService;
import com.lw.apcc.module.iot.service.warning.WarningService;
import com.lw.apcc.module.iot.utils.ProductWarningSettingUtil;
import com.lw.apcc.module.system.api.permission.dto.DataPermissionRespDTO;
import com.lw.apcc.module.system.api.user.dto.UserRespDTO;
import com.lw.apcc.module.system.cache.DataPermissionCache;
import com.lw.apcc.module.system.cache.UserCache;
import com.lw.apcc.module.system.enums.permission.DataPermissionCodeEnum;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Map;
import java.util.Set;

import static com.lw.apcc.common.exception.util.ServiceExceptionUtil.exception;

/**
 * <AUTHOR> yang
 * @description 设备预警
 * @date 2024/9/26 20:29
 */
@Slf4j
@Service
public class DeviceWarningServiceImpl implements DeviceWarningService {

    @Resource
    private DeviceWarningMapper baseMapper;

    @Resource
    private ProductWarningSettingService productWarningSettingService;

    @Resource
    private AssetRelMapper assetRelMapper;

    @Resource
    private WarningGlobalSettingService globalSettingService;

    @Resource
    private WarningService warningService;

    @Resource
    private DeviceMapper deviceMapper;

    /**
     * 分页查询资产设备预警
     *
     * @param pageReqVO
     * @return
     */
    @Override
    public PageResult<AssetDeviceWarningRespVO> pageAssetDeviceWarning(AssetDeviceWarningPageReqVO pageReqVO) {
        // 数据权限
        DataPermissionRespDTO dataPermission = DataPermissionCache.getDataPermissionByCode(DataPermissionCodeEnum.UNIT);

        // 资产设备预警
        IPage<AssetDeviceWarningRespVO> iPage = baseMapper.pageAssetDeviceWarning(MyBatisUtils.buildPage(pageReqVO), pageReqVO, dataPermission);

        List<AssetDeviceWarningRespVO> assetWarningList = iPage.getRecords();
        if (ObjectUtil.isEmpty(assetWarningList)) {
            return PageResult.empty();
        }

        // 资产id
        Set<Long> assetIds = CollectionUtils.convertSet(assetWarningList, AssetDeviceWarningRespVO::getAssetId);
        // 产品id
        Set<Long> productIds = CollectionUtils.convertSet(assetWarningList, AssetDeviceWarningRespVO::getProductId);

        // 产品预警设置
        Map<Long, Map<String, List<ProductWarningSettingDO>>> productIdToSettings = getProductWarningSetting(productIds);

        // 资产传感器范围预警
        Map<String, List<AssetDeviceSensorRangeWarningRespVO>> sensorIdToRangeWarning = getAssetDeviceSensorRangeWarning(assetIds, productIds);

        assetWarningList.forEach(
                assetWarning -> {
                    // 加载传感器预警信息
                    List<AssetDeviceSensorWarningRespVO> sensorWarningList = initSensorWarning(assetWarning, sensorIdToRangeWarning, productIdToSettings.get(assetWarning.getProductId()));

                    assetWarning.setSensorWarningList(sensorWarningList);
                }
        );
        return MyBatisUtils.buildPageResult(iPage);
    }

    /**
     * 加载传感器预警信息
     *
     * @param assetWarning
     * @param sensorIdToRangeWarning
     * @param settings
     */
    private List<AssetDeviceSensorWarningRespVO> initSensorWarning(AssetDeviceWarningRespVO assetWarning, Map<String, List<AssetDeviceSensorRangeWarningRespVO>> sensorIdToRangeWarning, Map<String, List<ProductWarningSettingDO>> settings) {
        if (ObjectUtil.isEmpty(settings)) {
            return Lists.newArrayList();
        }
        List<AssetDeviceSensorWarningRespVO> sensorWarningList = Lists.newArrayList();

        settings.forEach(
                (settingKey, warningSettingList) -> {
                    // settingType + sensorId/warningTypeId
                    String[] settingKeys = settingKey.split("@");
                    if (settingKeys.length != 2) {
                        return;
                    }
                    // 初始化传感器预警信息
                    AssetDeviceSensorWarningRespVO sensorWarning = convertDeviceSensorWarning(assetWarning, settingKeys);

                    Map<String, List<AssetDeviceSensorRangeWarningRespVO>> rangeTypeToRangeWarnings = Maps.newHashMap();

                    warningSettingList.forEach(
                            warningSetting -> {

                                // key = assetId + productId + settingType + sensorId/warningTypeId + rangeType/-1
                                String rangeType = StrUtil.emptyToDefault(warningSetting.getRangeType(), "-1");
                                String key = String.format("%s@%s@%s@%s", assetWarning.getAssetId(), assetWarning.getProductId(), settingKey, rangeType);

                                // 获取预警范围信息
                                List<AssetDeviceSensorRangeWarningRespVO> rangeWarningList = sensorIdToRangeWarning.get(key);
                                if (ObjectUtil.isEmpty(rangeWarningList)) {
                                    rangeWarningList = Lists.newArrayList();
                                }

                                // 计算未开启预警数量
                                Integer deviceCount = CollectionUtils.getSumValue(rangeWarningList, AssetDeviceSensorRangeWarningRespVO::getDeviceCount, Integer::sum, 0);
                                if (!assetWarning.getDeviceCount().equals(deviceCount) && assetWarning.getDeviceCount() > deviceCount) {
                                    int disableCount = assetWarning.getDeviceCount() - deviceCount;
                                    AssetDeviceSensorRangeWarningRespVO disableWarning = initDisableDeviceSensorRangeWarning(assetWarning, settingKeys, disableCount);

                                    rangeWarningList.add(disableWarning);
                                }

                                rangeTypeToRangeWarnings.put(rangeType, rangeWarningList);

                            }
                    );
                    sensorWarning.setRangeTypeToRangeWarnings(rangeTypeToRangeWarnings);

                    sensorWarningList.add(sensorWarning);
                }
        );

        return sensorWarningList;
    }

    /**
     * 初始化禁用的传感器预警信息
     *
     * @param assetWarning
     * @param settingKeys
     * @param disableCount
     * @return
     */
    private AssetDeviceSensorRangeWarningRespVO initDisableDeviceSensorRangeWarning(AssetDeviceWarningRespVO assetWarning, String[] settingKeys, int disableCount) {

        AssetDeviceSensorRangeWarningRespVO disableWarning = AssetDeviceSensorRangeWarningRespVO.builder().assetId(assetWarning.getAssetId()).productId(assetWarning.getProductId()).build();

        Integer settingType = Integer.valueOf(settingKeys[0]);
        disableWarning.setSettingType(settingType);

        disableWarning.setDeviceCount(disableCount);
        disableWarning.setStatus(CommonStatusEnum.DISABLE.getStatus());

        if (ProductWarningSettingConstants.SETTING_TYPE_GENERAL.equals(settingType)) {
            disableWarning.setWarningTypeId(Long.valueOf(settingKeys[1]));
        } else if (ProductWarningSettingConstants.SETTING_TYPE_CUSTOM.equals(settingType)) {
            disableWarning.setSensorId(Long.valueOf(settingKeys[1]));
        }
        return disableWarning;
    }

    /**
     * 预警设置key 转为预警信息
     *
     * @param assetWarning
     * @param settingKeys
     * @return
     */
    private AssetDeviceSensorWarningRespVO convertDeviceSensorWarning(AssetDeviceWarningRespVO assetWarning, String[] settingKeys) {
        AssetDeviceSensorWarningRespVO sensorWarning = AssetDeviceSensorWarningRespVO.builder().assetId(assetWarning.getAssetId()).productId(assetWarning.getProductId()).build();

        Integer settingType = Integer.valueOf(settingKeys[0]);
        sensorWarning.setSettingType(settingType);

        if (ProductWarningSettingConstants.SETTING_TYPE_GENERAL.equals(settingType)) {
            Long warningTypeId = Long.valueOf(settingKeys[1]);
            sensorWarning.setWarningTypeId(warningTypeId);
            sensorWarning.setWarningTypeName(WarningTypeCache.getNameById(warningTypeId));
        } else if (ProductWarningSettingConstants.SETTING_TYPE_CUSTOM.equals(settingType)) {
            SensorRespDTO sensor = SensorCache.getById(Long.valueOf(settingKeys[1]));
            if (sensor != null) {
                sensorWarning.setSensorId(sensor.getId());
                sensorWarning.setSensorName(sensor.getName());
                sensorWarning.setSensorAlias(ObjectUtil.defaultIfNull(sensor.getAlias(), sensor.getName()));
                sensorWarning.setSensorUnit(sensor.getUnit());
            }
        }
        return sensorWarning;
    }

    /**
     * 产品预警设置
     *
     * @param productIds
     * @return
     */
    private Map<Long, Map<String, List<ProductWarningSettingDO>>> getProductWarningSetting(Set<Long> productIds) {
        List<ProductWarningSettingDO> settingList = productWarningSettingService.listByProductIds(productIds);
        if (ObjectUtil.isEmpty(settingList)) {
            return Maps.newHashMap();
        }
        Map<Long, Map<String, List<ProductWarningSettingDO>>> productIdToSettings = Maps.newHashMap();

        // 按产品分组
        Map<Long, List<ProductWarningSettingDO>> productIdToWarningSettings = CollectionUtils.groupingBy(settingList, ProductWarningSettingDO::getProductId);
        productIdToWarningSettings.forEach(
                (productId, productWarningSettingList) -> {

                    // 转换产品预警设置分组
                    Map<String, List<ProductWarningSettingDO>> settings = convertSettingKeyToWarningSettings(productWarningSettingList);

                    productIdToSettings.put(productId, settings);
                }
        );
        return productIdToSettings;
    }

    /**
     * 转换预警设置key为预警设置分组
     *
     * @param productWarningSettingList
     * @return
     */
    private Map<String, List<ProductWarningSettingDO>> convertSettingKeyToWarningSettings(List<ProductWarningSettingDO> productWarningSettingList) {
        Map<String, List<ProductWarningSettingDO>> settings = Maps.newHashMap();
        productWarningSettingList.forEach(
                setting -> {
                    // key = settingType + sensorId/warningTypeId
                    String settingKey = String.format("%s@%s", setting.getSettingType(), ObjectUtil.defaultIfNull(setting.getWarningTypeId(), setting.getSensorId()));

                    List<ProductWarningSettingDO> warningSettingList = settings.get(settingKey);
                    if (warningSettingList == null) {
                        warningSettingList = Lists.newArrayList();
                    }
                    warningSettingList.add(setting);

                    settings.put(settingKey, warningSettingList);
                }
        );
        return settings;
    }

    /**
     * 资产传感器范围预警
     *
     * @param assetId
     * @param productId
     * @return
     */
    private Map<String, List<AssetDeviceSensorRangeWarningRespVO>> getAssetDeviceSensorRangeWarning(Long assetId, Long productId) {
        return getAssetDeviceSensorRangeWarning(Sets.newHashSet(assetId), Sets.newHashSet(productId));
    }

    /**
     * 资产传感器范围预警
     *
     * @param assetIds
     * @param productIds
     * @return
     */
    private Map<String, List<AssetDeviceSensorRangeWarningRespVO>> getAssetDeviceSensorRangeWarning(Set<Long> assetIds, Set<Long> productIds) {
        Map<String, List<AssetDeviceSensorRangeWarningRespVO>> sensorIdToRangeWarning = Maps.newHashMap();
        // 资产传感器预警
        List<AssetDeviceSensorRangeWarningRespVO> rangeWarningList = baseMapper.listAssetDeviceSensorRangeWarning(assetIds, productIds);
        rangeWarningList.forEach(
                rangeWarning -> {
                    // key = assetId + productId + settingType + sensorId/warningTypeId + rangeType/-1
                    String key = String.format("%s@%s@%s@%s@%s", rangeWarning.getAssetId(), rangeWarning.getProductId(), rangeWarning.getSettingType(), ObjectUtil.defaultIfNull(rangeWarning.getWarningTypeId(), rangeWarning.getSensorId()), StrUtil.emptyToDefault(rangeWarning.getRangeType(), "-1"));

                    List<AssetDeviceSensorRangeWarningRespVO> sensorRangeWarningList = sensorIdToRangeWarning.get(key);
                    if (CollectionUtils.isEmpty(sensorRangeWarningList)) {
                        sensorRangeWarningList = Lists.newArrayList();
                    }
                    sensorRangeWarningList.add(rangeWarning);

                    sensorIdToRangeWarning.put(key, sensorRangeWarningList);
                }
        );
        return sensorIdToRangeWarning;
    }

    /**
     * 列表查询设备预警
     *
     * @param reqVO
     * @return
     */
    @Override
    public List<DeviceWarningRespVO> listDeviceWarning(DeviceWarningReqVO reqVO) {
        // 设备传感器
        List<DeviceSensorWarningRespVO> deviceSensorWarningList = baseMapper.listDeviceSensorWarningList(reqVO);

        // 按照设备分组
        Map<Long, List<DeviceSensorWarningRespVO>> deviceIdToSensorWarningList = CollectionUtils.groupingBy(deviceSensorWarningList, DeviceSensorWarningRespVO::getDeviceId);

        // 转换
        List<DeviceWarningRespVO> deviceWarningList = convertDeviceWarning(deviceIdToSensorWarningList);

        return deviceWarningList;
    }

    /**
     * 转换设备预警信息
     *
     * @param deviceIdToSensorWarningList
     * @return
     */
    private List<DeviceWarningRespVO> convertDeviceWarning(Map<Long, List<DeviceSensorWarningRespVO>> deviceIdToSensorWarningList) {

        List<DeviceWarningRespVO> deviceWarningList = Lists.newArrayList();

        deviceIdToSensorWarningList.forEach(
                (deviceId, sensorWarningList) -> {
                    DeviceWarningRespVO deviceWarning = new DeviceWarningRespVO();

                    DeviceSensorWarningRespVO sensorWarning = sensorWarningList.get(0);
                    deviceWarning.setId(deviceId);
                    deviceWarning.setName(sensorWarning.getDeviceName());
                    deviceWarning.setCode(sensorWarning.getDeviceCode());
                    deviceWarning.setProductId(sensorWarning.getProductId());

                    // 设备传感器预警集合
                    Map<Long, List<DeviceSensorWarningRespVO>> sensorIdToWarningList = convertSensorIdToWarningList(sensorWarningList);
                    deviceWarning.setSensorIdToWarningList(sensorIdToWarningList);

                    deviceWarningList.add(deviceWarning);
                }
        );

        return deviceWarningList;
    }

    /**
     * 设备传感器预警集合
     *
     * @param sensorWarningList
     * @return
     */
    private Map<Long, List<DeviceSensorWarningRespVO>> convertSensorIdToWarningList(List<DeviceSensorWarningRespVO> sensorWarningList) {
        Map<Long, List<DeviceSensorWarningRespVO>> sensorIdToWarningList = Maps.newHashMap();
        sensorWarningList.forEach(
                sensorWarning -> {
                    Long key = ProductWarningSettingConstants.SETTING_TYPE_GENERAL.equals(sensorWarning.getSettingType()) ? sensorWarning.getWarningTypeId() : sensorWarning.getSensorId();

                    List<DeviceSensorWarningRespVO> sensorWarningListBySensorId = sensorIdToWarningList.get(key);
                    if (CollectionUtils.isEmpty(sensorWarningListBySensorId)) {
                        sensorWarningListBySensorId = Lists.newArrayList();
                    }

                    sensorWarningListBySensorId.add(sensorWarning);
                    sensorIdToWarningList.put(key, CollectionUtils.sortList(sensorWarningListBySensorId, DeviceSensorWarningRespVO::getRangeType));
                }
        );
        return sensorIdToWarningList;
    }

    /**
     * 查询资产设备预警详情
     *
     * @param detailsReqVO
     * @return
     */
    @Override
    public AssetDeviceWarningDetailsRespVO getAssetDeviceWarningDetails(AssetDeviceWarningDetailsReqVO detailsReqVO) {

        // 资产设备预警
        AssetDeviceWarningDetailsRespVO assetWarning = new AssetDeviceWarningDetailsRespVO();

        // 基础信息
        initBaseAssetDeviceWarningDetails(assetWarning, detailsReqVO);

        // 预警全局设置
        WarningGlobalSettingDO globalSetting = globalSettingService.getGlobalSetting();

        // 设备预警设置
        initSettingAssetDeviceWarningDetails(assetWarning, detailsReqVO, globalSetting);

        // 预警方式信息
        assetWarning.setActionIds(Sets.newHashSet());

        // 预警高级设置
        DeviceWarningAdvancedVO advancedSetting = new DeviceWarningAdvancedVO();
        advancedSetting.setDelayMinutes(globalSetting.getDelayMinutes());
        advancedSetting.setIsRepeat(globalSetting.getIsRepeat());
        advancedSetting.setRepeatCount(globalSetting.getRepeatCount());
        advancedSetting.setRepeatSpacingMinutes(globalSetting.getRepeatSpacingMinutes());
        advancedSetting.setRepeatMinutes(globalSetting.getRepeatMinutes());
        advancedSetting.setStartTime(globalSetting.getStartTime());
        advancedSetting.setEndTime(globalSetting.getEndTime());
        assetWarning.setAdvancedSetting(advancedSetting);

        return assetWarning;
    }

    /**
     * 设备预警设置
     *
     * @param assetWarning
     * @param detailsReqVO
     */
    private void initSettingAssetDeviceWarningDetails(AssetDeviceWarningDetailsRespVO assetWarning, AssetDeviceWarningDetailsReqVO detailsReqVO, WarningGlobalSettingDO globalSetting) {

        List<DeviceWarningSettingDetailsVO> warningSettingList = Lists.newLinkedList();

        List<ProductWarningSettingDO> productWarningSettingList = productWarningSettingService.listByProductIds(Sets.newHashSet(detailsReqVO.getProductId()));
        if (ObjectUtil.isEmpty(productWarningSettingList)) {
            throw exception("该产品未设置/未开启预警");
        }

        productWarningSettingList.forEach(
                warningSetting -> {
                    DeviceWarningSettingDetailsVO settingDetails = new DeviceWarningSettingDetailsVO();

                    settingDetails.setId(warningSetting.getId());
                    settingDetails.setSettingType(warningSetting.getSettingType());

                    if (ProductWarningSettingConstants.SETTING_TYPE_GENERAL.equals(warningSetting.getSettingType())) {
                        WarningTypeRespDTO warningType = WarningTypeCache.getById(warningSetting.getWarningTypeId());
                        if (warningType == null) {
                            throw exception("未查询到预警类型！");
                        }
                        settingDetails.setRuleType(warningType.getRuleType());
                        settingDetails.setTypeCode(warningType.getTypeCode());
                        settingDetails.setWarningTypeId(warningType.getId());
                        settingDetails.setSensorId(warningType.getSensorId());
                        settingDetails.setSensorUnit(SensorCache.getUnitById(warningType.getSensorId()));
                        settingDetails.setRangeType(warningType.getRangeType());
                        settingDetails.setFilterRange(warningType.getFilterRange());
                        settingDetails.setWarningRange(warningType.getDefaultWarningRange());

                        // 定制：离线 OFFLINE
                        if (WarningTypeConstants.RULE_TYPE_CODE_CUSTOM_OFFLINE.equals(warningType.getTypeCode())) {
                            settingDetails.setOfflineMinutes(globalSetting.getOfflineMinutes());
                        }
                    } else if (ProductWarningSettingConstants.SETTING_TYPE_CUSTOM.equals(warningSetting.getSettingType())) {
                        settingDetails.setRuleType(WarningTypeConstants.RULE_TYPE_GENERAL);
                        settingDetails.setSensorId(warningSetting.getSensorId());
                        settingDetails.setSensorUnit(SensorCache.getUnitById(warningSetting.getSensorId()));
                        settingDetails.setRangeType(warningSetting.getRangeType());
                        settingDetails.setFilterRange(warningSetting.getFilterRange());
                        settingDetails.setWarningRange(warningSetting.getDefaultWarningRange());
                    }
                    String name = ProductWarningSettingUtil.convertName(warningSetting.getSettingType(), warningSetting.getWarningTypeId(), warningSetting.getSensorId(), warningSetting.getRangeType());
                    settingDetails.setName(name);
                    settingDetails.setStatus(CommonStatusEnum.DISABLE.getStatus());

                    warningSettingList.add(settingDetails);
                }
        );

        assetWarning.setWarningSettingList(warningSettingList);
    }

    /**
     * 基础信息
     *
     * @param detailsReqVO
     */
    private void initBaseAssetDeviceWarningDetails(AssetDeviceWarningDetailsRespVO assetWarning, AssetDeviceWarningDetailsReqVO detailsReqVO) {
        List<AssetRelDetailsRespDTO> assetRelDetailsList = assetRelMapper.listAssetRelDetailsByIds(detailsReqVO.getAssetIds());
        if (ObjectUtil.isEmpty(assetRelDetailsList)) {
            throw exception("未查询到资产信息！");
        }

        assetWarning.setUnitId(assetRelDetailsList.get(0).getUnitId());
        assetWarning.setUnitName(assetRelDetailsList.get(0).getUnitName());
        assetWarning.setAssetIds(CollectionUtils.convertSet(assetRelDetailsList, AssetRelDetailsRespDTO::getId));
        assetWarning.setAssetNames(StringUtil.join(CollectionUtils.convertSet(assetRelDetailsList, AssetRelDetailsRespDTO::getName), "、"));
        assetWarning.setProductId(detailsReqVO.getProductId());
        assetWarning.setProductName(ProductCache.getNameById(detailsReqVO.getProductId()));
    }

    /**
     * 查询设备预警详情
     *
     * @param deviceId
     * @return
     */
    @Override
    public DeviceWarningDetailsRespVO getDeviceWarningDetails(Long deviceId) {

        // 设备预警
        DeviceWarningDetailsRespVO detailsReqVO = baseMapper.getDeviceWarningDetails(deviceId);
        if (ObjectUtil.isEmpty(detailsReqVO)) {
            throw exception("未查询到设备信息！");
        }

        // 预警全局设置
        WarningGlobalSettingDO globalSetting = globalSettingService.getGlobalSetting();

        // 预警
        List<WarningDO> warningList = warningService.list(Wrappers.<WarningDO>lambdaQuery().eq(WarningDO::getDeviceId, deviceId));
        // 设备预警设置
        initSettingDeviceWarningDetails(detailsReqVO, globalSetting, CollectionUtils.toMap(warningList, WarningDO::getProductWarningSettingId));

        // 高级设置
        DeviceWarningAdvancedVO advancedSetting = new DeviceWarningAdvancedVO();

        if (ObjectUtil.isNotEmpty(warningList)) {
            WarningDO warning = warningList.get(0);
            // 预警方式信息
            detailsReqVO.setActionIds(warning.getActionIds());

            // 预警高级设置
            advancedSetting.setDelayMinutes(warning.getDelayMinutes());
            advancedSetting.setIsRepeat(warning.getIsRepeat());
            advancedSetting.setRepeatCount(warning.getRepeatCount());
            advancedSetting.setRepeatSpacingMinutes(warning.getRepeatSpacingMinutes());
            advancedSetting.setRepeatMinutes(warning.getRepeatMinutes());
            advancedSetting.setStartTime(warning.getStartTime());
            advancedSetting.setEndTime(warning.getEndTime());
        } else {
            // 预警方式信息
            detailsReqVO.setActionIds(Sets.newHashSet());

            // 预警高级设置
            advancedSetting.setDelayMinutes(globalSetting.getDelayMinutes());
            advancedSetting.setIsRepeat(globalSetting.getIsRepeat());
            advancedSetting.setRepeatCount(globalSetting.getRepeatCount());
            advancedSetting.setRepeatSpacingMinutes(globalSetting.getRepeatSpacingMinutes());
            advancedSetting.setRepeatMinutes(globalSetting.getRepeatMinutes());
            advancedSetting.setStartTime(globalSetting.getStartTime());
            advancedSetting.setEndTime(globalSetting.getEndTime());
        }

        detailsReqVO.setAdvancedSetting(advancedSetting);
        return detailsReqVO;
    }

    /**
     * 初始化预警设置
     *
     * @param detailsReqVO
     * @param globalSetting
     */
    private void initSettingDeviceWarningDetails(DeviceWarningDetailsRespVO detailsReqVO, WarningGlobalSettingDO globalSetting, Map<Long, WarningDO> settingIdToWarning) {

        List<DeviceWarningSettingDetailsVO> warningSettingList = Lists.newLinkedList();

        List<ProductWarningSettingDO> productWarningSettingList = productWarningSettingService.listByProductIds(Sets.newHashSet(detailsReqVO.getProductId()));
        if (ObjectUtil.isEmpty(productWarningSettingList)) {
            throw exception("该产品未设置/未开启预警");
        }

        productWarningSettingList.forEach(
                warningSetting -> {
                    DeviceWarningSettingDetailsVO settingDetails = new DeviceWarningSettingDetailsVO();

                    settingDetails.setId(warningSetting.getId());
                    settingDetails.setSettingType(warningSetting.getSettingType());

                    if (ProductWarningSettingConstants.SETTING_TYPE_GENERAL.equals(warningSetting.getSettingType())) {
                        WarningTypeRespDTO warningType = WarningTypeCache.getById(warningSetting.getWarningTypeId());
                        if (warningType == null) {
                            throw exception("未查询到预警类型！");
                        }

                        settingDetails.setRuleType(warningType.getRuleType());
                        settingDetails.setTypeCode(warningType.getTypeCode());
                        settingDetails.setWarningTypeId(warningType.getId());
                        settingDetails.setSensorId(warningType.getSensorId());
                        settingDetails.setSensorUnit(SensorCache.getUnitById(warningType.getSensorId()));
                        settingDetails.setRangeType(warningType.getRangeType());
                        settingDetails.setFilterRange(warningType.getFilterRange());
                        settingDetails.setWarningRange(warningType.getDefaultWarningRange());

                        // 定制：离线 OFFLINE
                        if (WarningTypeConstants.RULE_TYPE_CODE_CUSTOM_OFFLINE.equals(warningType.getTypeCode())) {
                            settingDetails.setOfflineMinutes(globalSetting.getOfflineMinutes());
                        }
                    } else if (ProductWarningSettingConstants.SETTING_TYPE_CUSTOM.equals(warningSetting.getSettingType())) {
                        settingDetails.setRuleType(WarningTypeConstants.RULE_TYPE_GENERAL);
                        settingDetails.setSensorId(warningSetting.getSensorId());
                        settingDetails.setSensorUnit(SensorCache.getUnitById(warningSetting.getSensorId()));
                        settingDetails.setRangeType(warningSetting.getRangeType());
                        settingDetails.setFilterRange(warningSetting.getFilterRange());
                        settingDetails.setWarningRange(warningSetting.getDefaultWarningRange());
                    }
                    String name = ProductWarningSettingUtil.convertName(warningSetting.getSettingType(), warningSetting.getWarningTypeId(), warningSetting.getSensorId(), warningSetting.getRangeType());
                    settingDetails.setName(name);
                    settingDetails.setStatus(CommonStatusEnum.DISABLE.getStatus());

                    // 预警设置
                    WarningDO warning = settingIdToWarning.get(warningSetting.getId());
                    if (warning != null && CommonStatusEnum.ENABLE.getStatus().equals(warning.getStatus())) {
                        try {
                            settingDetails.setWarningRange(JSON.parseArray(warning.getWarningRange()));
                            settingDetails.setOfflineMinutes(warning.getOfflineMinutes());
                            settingDetails.setStatus(CommonStatusEnum.ENABLE.getStatus());
                        } catch (Exception ex) {
                            ex.getMessage();
                        }
                    }
                    warningSettingList.add(settingDetails);
                }
        );

        detailsReqVO.setWarningSettingList(warningSettingList);
    }

    /**
     * 保存设备预警信息
     *
     * @param saveReqVO
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public Boolean saveByDeviceWarning(DeviceWarningSaveReqVO saveReqVO) {
        // 设备
        DeviceDO device = deviceMapper.selectById(saveReqVO.getId());
        if (device == null) {
            throw exception("未查询到设备！");
        }
        // 校验预警设置
        if (ObjectUtil.isEmpty(saveReqVO.getWarningSettingList())) {
            throw exception("请前往产品管理设置预警信息！");
        }
        // 产品预警设置ID - 预警信息
        Map<Long, WarningDO> settingIdToWarning = warningService.getWarningSettingByDeviceId(saveReqVO.getId());

        // 预警设置Id
        Set<Long> settingIds = CollectionUtils.convertSet(saveReqVO.getWarningSettingList(), DeviceWarningSettingDetailsVO::getId);

        // 删除非产品预警设置
        warningService.removeByDeviceId(device.getId(), device.getProductId(), settingIds);

        // 转换预警信息
        List<WarningDO> warningList = convertWarning(device, saveReqVO.getActionIds(), saveReqVO.getAdvancedSetting(), saveReqVO.getWarningSettingList(), settingIdToWarning);

        return warningService.saveOrUpdateBatch(warningList);
    }

    /**
     * 一键全部执行
     *
     * @param saveReqVO
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public Boolean batchSaveByAssetDeviceWarning(AssetDeviceWarningSaveReqVO saveReqVO) {
        // 设备
        List<DeviceDO> deviceList = deviceMapper.listByAssetIds(saveReqVO.getAssetIds(), saveReqVO.getProductId());
        if (ObjectUtil.isEmpty(deviceList)) {
            throw exception("未查询到设备！");
        }
        // 校验预警设置
        if (ObjectUtil.isEmpty(saveReqVO.getWarningSettingList())) {
            throw exception("请前往产品管理设置预警信息！");
        }

        // 预警设置Id
        Set<Long> settingIds = CollectionUtils.convertSet(saveReqVO.getWarningSettingList(), DeviceWarningSettingDetailsVO::getId);

        // 删除非产品预警设置
        warningService.removeByAssetIds(saveReqVO.getAssetIds(), saveReqVO.getProductId(), settingIds);

        // 产品预警设置ID - 预警信息
        Map<Long, Map<Long, WarningDO>> deviceIdToWarning = warningService.getWarningSettingByDeviceIds(CollectionUtils.convertSet(deviceList, DeviceDO::getId));

        // 转换预警信息
        List<WarningDO> warningList = convertAssetWarning(deviceList, saveReqVO, deviceIdToWarning);

        return warningService.saveOrUpdateBatch(warningList);
    }

    /**
     * 根据资产id查询产品列表
     *
     * @param assetId
     * @return
     */
    @Override
    public List<AppAssetProductRespVO> listAssetProduct(Long assetId) {
        return baseMapper.listAssetProduct(assetId);
    }

    /**
     * 根据资产id和产品id查询设备预警
     *
     * @param assetId
     * @param productId
     * @return
     */
    @Override
    public List<AssetDeviceSensorWarningRespVO> listAssetDeviceSensorWarning(Long assetId, Long productId) {

        // 设备预警
        AssetDeviceWarningRespVO deviceWarning = baseMapper.getAssetDeviceWarning(assetId, productId);

        // 产品预警设置
        Map<String, List<ProductWarningSettingDO>> settings = getProductWarningSetting(productId);

        // 资产传感器范围预警
        Map<String, List<AssetDeviceSensorRangeWarningRespVO>> sensorIdToRangeWarning = getAssetDeviceSensorRangeWarning(assetId, productId);

        List<AssetDeviceSensorWarningRespVO> sensorWarningList = initSensorWarning(deviceWarning, sensorIdToRangeWarning, settings);

        return sensorWarningList;
    }

    /**
     * @param deviceId
     * @description 根据设备id查询监测预警信息
     * <AUTHOR> yang
     * @date 2024/11/4 17:24
     */
    @Override
    public Map<String, List<DeviceMonitorWarningRespVO>> getDeviceMonitorWarning(Long deviceId) {
        Map<Long, Map<String, List<DeviceMonitorWarningRespVO>>> deviceMonitorWarning = getDeviceMonitorWarning(Sets.newHashSet(deviceId));
        if (ObjectUtil.isEmpty(deviceMonitorWarning)) {
            return null;
        }
        return deviceMonitorWarning.get(deviceId);
    }

    /**
     * 产品预警设置
     *
     * @param productId
     * @return
     */
    private Map<String, List<ProductWarningSettingDO>> getProductWarningSetting(Long productId) {
        List<ProductWarningSettingDO> settingList = productWarningSettingService.listByProductId(productId);
        if (ObjectUtil.isEmpty(settingList)) {
            return Maps.newHashMap();
        }

        // 转换产品预警设置分组
        Map<String, List<ProductWarningSettingDO>> settings = convertSettingKeyToWarningSettings(settingList);

        return settings;
    }

    /**
     * 转换预警信息
     *
     * @param saveReqVO
     * @param deviceIdToWarning
     * @return
     */
    private List<WarningDO> convertAssetWarning(List<DeviceDO> deviceList, AssetDeviceWarningSaveReqVO saveReqVO, Map<Long, Map<Long, WarningDO>> deviceIdToWarning) {
        // 预警集合
        List<WarningDO> assetWarningList = Lists.newArrayList();

        deviceList.forEach(
                device -> {
                    // 预警设置
                    Map<Long, WarningDO> settingIdToWarning = deviceIdToWarning.get(device.getId());
                    if (ObjectUtil.isEmpty(settingIdToWarning)) {
                        settingIdToWarning = Maps.newHashMap();
                    }

                    // 转换预警信息
                    List<WarningDO> warningList = convertWarning(device, saveReqVO.getActionIds(), saveReqVO.getAdvancedSetting(), saveReqVO.getWarningSettingList(), settingIdToWarning);

                    assetWarningList.addAll(warningList);
                }
        );

        return assetWarningList;
    }

    /**
     * @description 转换预警信息
     * <AUTHOR> yang
     * @date 2024/10/11 17:13
     */
    private List<WarningDO> convertWarning(DeviceDO device, Set<Long> actionIds, DeviceWarningAdvancedVO advancedSetting, List<DeviceWarningSettingDetailsVO> warningSettingList, Map<Long, WarningDO> settingIdToWarning) {
        // 预警集合
        List<WarningDO> warningList = Lists.newArrayList();

        // 当前用户
        UserRespDTO user = UserCache.getById(SecurityUtils.getLoginUserId());

        // 预警类型字典
        Map<String, String> rangeTypeMap = DictCaChe.getByType(DictTypeConstants.IOT_WARNING_TYPE_RANGE_TYPE);

        warningSettingList.forEach(
                warningSetting -> {
                    WarningDO warning = settingIdToWarning.get(warningSetting.getId());
                    if (warning == null) {
                        warning = new WarningDO();
                    }

                    warning.setType(warningSetting.getRangeType());

                    // 基础信息
                    warning.setUnitId(device.getUnitId());
                    warning.setAssetId(device.getAssetId());
                    warning.setDeviceId(device.getId());
                    warning.setProductId(device.getProductId());

                    // 预警规则逻辑处理
                    warning = initWarningSetting(warningSetting, warning, rangeTypeMap, device);
                    if (warning == null) {
                        log.info(String.format("设备：%s 规则：%s 预警规则逻辑处理失败！", device.getCode(), warningSetting.getName()));
                        return;
                    }

                    // 预警设置
                    warning.setStatus(warningSetting.getStatus());
                    String warningRange = convertWarningRange(warningSetting);
                    warning.setWarningRange(warningRange);
                    warning.setProductWarningSettingId(warningSetting.getId());

                    // 重复预警
                    warning.setDelayMinutes(advancedSetting.getDelayMinutes());
                    warning.setIsRepeat(advancedSetting.getIsRepeat());
                    warning.setRepeatMinutes(advancedSetting.getRepeatMinutes());
                    warning.setRepeatCount(advancedSetting.getRepeatCount());
                    warning.setRepeatSpacingMinutes(advancedSetting.getRepeatSpacingMinutes());
                    warning.setStartTime(advancedSetting.getStartTime());
                    warning.setEndTime(advancedSetting.getEndTime());

                    // 预警方式
                    warning.setActionIds(actionIds);

                    // 预警设置人
                    warning.setSettingUserId(user.getId());
                    warning.setSettingUserName(user.getNickname());

                    warningList.add(warning);
                }
        );

        return warningList;
    }

    /**
     * 转换预警范围
     *
     * @param warningSetting
     * @return
     */
    private String convertWarningRange(DeviceWarningSettingDetailsVO warningSetting) {
        JSONArray warningRange = warningSetting.getWarningRange();
        if (warningRange != null && warningRange.size() > 0 && (WarningRangeTypeEnum.OVER_UPPER.equalsCode(warningSetting.getRangeType()) || WarningRangeTypeEnum.OVER_LOWER.equalsCode(warningSetting.getRangeType()))) {
            return String.format("[\"%s\"]", warningRange.getString(0));
        }
        return warningSetting.getWarningRange().toJSONString();
    }

    /**
     * 预警设置逻辑
     *
     * @param warningSetting
     * @param warning
     */
    private WarningDO initWarningSetting(DeviceWarningSettingDetailsVO warningSetting, WarningDO warning, Map<String, String> rangeTypeMap, DeviceDO device) {
        // 规则类型
        warning.setRuleType(warningSetting.getRuleType());
        // 预警类型Id
        warning.setWarningTypeId(warningSetting.getWarningTypeId());

        // 定制
        if (WarningTypeConstants.RULE_TYPE_CUSTOM.equals(warningSetting.getRuleType())) {
            return initWarningSettingCustom(warningSetting, warning, device);
        }
        // 常规
        return initWarningSettingGeneral(warningSetting, warning, rangeTypeMap, device);
    }

    /**
     * 常规预警设置
     *
     * @param warningSetting
     * @param warning
     * @return
     */
    private WarningDO initWarningSettingGeneral(DeviceWarningSettingDetailsVO warningSetting, WarningDO warning, Map<String, String> rangeTypeMap, DeviceDO device) {

        SensorRespDTO sensor = SensorCache.getById(warningSetting.getSensorId());

        // 预警名称
        String name = ProductWarningSettingUtil.convertName(warningSetting.getSettingType(), warningSetting.getWarningTypeId(), warningSetting.getSensorId(), warningSetting.getRangeType());
        warning.setName(name);

        warning.setWarningField(sensor.getSlug());
        warning.setSensorId(sensor.getId());

        return warning;
    }

    /**
     * 定制预警设置
     *
     * @param warningSetting
     * @param warning
     * @return
     */
    private WarningDO initWarningSettingCustom(DeviceWarningSettingDetailsVO warningSetting, WarningDO warning, DeviceDO device) {
        // 离线
        if (WarningTypeConstants.RULE_TYPE_CODE_CUSTOM_OFFLINE.equals(warningSetting.getTypeCode())) {

            warning.setName("离线预警");
            warning.setWarningField(WarningTypeConstants.RULE_TYPE_CODE_CUSTOM_OFFLINE);

            warning.setOfflineMinutes(warningSetting.getOfflineMinutes());

            return warning;
        }

        // 未处理开发的定制逻辑 返回空
        return null;
    }


    /**
     * @description 根据设备id查询设备监测预警信息
     * <AUTHOR> yang
     * @date 2024/11/4 16:33
     */
    @Override
    public Map<Long, Map<String, List<DeviceMonitorWarningRespVO>>> getDeviceMonitorWarning(Set<Long> deviceIds) {
        List<WarningDO> warningList = warningService.listByDeviceIds(deviceIds);
        if (ObjectUtil.isEmpty(warningList)) {
            return Maps.newHashMap();
        }

        Map<Long, ProductWarningSettingDO> warningIdToWarningSetting = convertProductWarningSetting(CollectionUtils.convertSet(warningList, WarningDO::getProductWarningSettingId));

        Map<Long, Map<String, List<DeviceMonitorWarningRespVO>>> deviceIdToWarning = Maps.newHashMap();

        Map<Long, List<WarningDO>> deviceIdToWarningList = CollectionUtils.groupingBy(warningList, WarningDO::getDeviceId);
        deviceIdToWarningList.forEach(
                (deviceId, warnings) -> {

                    Map<String, List<DeviceMonitorWarningRespVO>> codeToMonitorWarning = deviceIdToWarning.get(deviceId);

                    if (codeToMonitorWarning == null) {
                        codeToMonitorWarning = Maps.newHashMap();
                    }
                    // 加载监测预警信息
                    initMonitorWarning(warnings, codeToMonitorWarning, warningIdToWarningSetting);

                    deviceIdToWarning.put(deviceId, codeToMonitorWarning);
                }
        );

        return deviceIdToWarning;
    }

    /**
     * @param warningIds
     * @description 根据预警id查询监测预警信息
     * <AUTHOR> yang
     * @date 2024/11/4 17:24
     */
    @Override
    public Map<Long, DeviceMonitorWarningRespVO> getDeviceMonitorWarningByWarningId(Set<Long> warningIds) {
        List<WarningDO> warningList = warningService.listByIds(warningIds);
        if (ObjectUtil.isEmpty(warningList)) {
            return Maps.newHashMap();
        }
        Map<Long, ProductWarningSettingDO> warningIdToWarningSetting = convertProductWarningSetting(CollectionUtils.convertSet(warningList, WarningDO::getProductWarningSettingId));
        if (ObjectUtil.isEmpty(warningIdToWarningSetting)) {
            return null;
        }

        Map<Long, DeviceMonitorWarningRespVO> warningIdToMonitorWarning = CollectionUtils.convertMap(
                warningList,
                WarningDO::getId,
                warning -> {
                    ProductWarningSettingDO warningSetting = warningIdToWarningSetting.get(warning.getProductWarningSettingId());
                    if (warningSetting == null) {
                        return null;
                    }

                    DeviceMonitorWarningRespVO monitorWarning = new DeviceMonitorWarningRespVO();

                    // 预警类型
                    String warningName = ProductWarningSettingUtil.convertAliasName(warningSetting.getSettingType(), warningSetting.getWarningTypeId(), warningSetting.getSensorId(), warningSetting.getRangeType());
                    monitorWarning.setWarningName(warningName);

                    // 预警范围
                    JSONArray warningRange = JSONArray.parseArray(warning.getWarningRange());
                    monitorWarning.setWarningRange(warningRange);

                    // 预警单位
                    if (warningSetting.getSensorId() != null) {
                        SensorRespDTO sensor = SensorCache.getById(warningSetting.getSensorId());
                        if (sensor != null) {
                            monitorWarning.setUnit(sensor.getUnit());
                        }
                    }

                    // 预警范围名称
                    WarningRangeTypeEnum rangeTypeEnum = WarningRangeTypeEnum.get(warningSetting.getRangeType());
                    if (rangeTypeEnum != null) {
                        monitorWarning.setWarningRangeName(rangeTypeEnum.convertValue(warningRange, monitorWarning.getUnit()));
                    }
                    return monitorWarning;
                }
        );
        return warningIdToMonitorWarning;
    }

    /**
     * 加载监测预警信息
     *
     * @param warnings
     * @param codeToMonitorWarning
     * @param warningIdToWarningSetting
     */
    private void initMonitorWarning(List<WarningDO> warnings, Map<String, List<DeviceMonitorWarningRespVO>> codeToMonitorWarning, Map<Long, ProductWarningSettingDO> warningIdToWarningSetting) {
        warnings.forEach(
                warning -> {

                    List<DeviceMonitorWarningRespVO> monitorWarningList = codeToMonitorWarning.get(warning.getWarningField());
                    if (monitorWarningList == null) {
                        monitorWarningList = Lists.newArrayList();
                    }
                    ProductWarningSettingDO warningSetting = warningIdToWarningSetting.get(warning.getProductWarningSettingId());
                    if (warningSetting == null) {
                        return;
                    }

                    DeviceMonitorWarningRespVO monitorWarning = new DeviceMonitorWarningRespVO();

                    // 预警类型
                    String warningName = ProductWarningSettingUtil.convertAliasName(warningSetting.getSettingType(), warningSetting.getWarningTypeId(), warningSetting.getSensorId(), warningSetting.getRangeType());
                    monitorWarning.setWarningName(warningName);

                    // 预警范围
                    JSONArray warningRange = JSONArray.parseArray(warning.getWarningRange());
                    monitorWarning.setWarningRange(warningRange);

                    // 预警单位
                    if (warningSetting.getSensorId() != null) {
                        SensorRespDTO sensor = SensorCache.getById(warningSetting.getSensorId());
                        if (sensor != null) {
                            monitorWarning.setUnit(sensor.getUnit());
                        }
                    }

                    // 预警范围名称
                    WarningRangeTypeEnum rangeTypeEnum = WarningRangeTypeEnum.get(warningSetting.getRangeType());
                    if (rangeTypeEnum != null) {
                        monitorWarning.setWarningRangeName(rangeTypeEnum.convertValue(warningRange, monitorWarning.getUnit()));
                    }

                    monitorWarningList.add(monitorWarning);

                    codeToMonitorWarning.put(warning.getWarningField(), monitorWarningList);
                }
        );
    }

    /**
     * 预警设置转换
     *
     * @param warningSettingIds
     * @return
     */
    private Map<Long, ProductWarningSettingDO> convertProductWarningSetting(Set<Long> warningSettingIds) {
        if (ObjectUtil.isEmpty(warningSettingIds)) {
            return Maps.newHashMap();
        }
        List<ProductWarningSettingDO> warningSettingList = productWarningSettingService.listByIds(warningSettingIds);
        if (ObjectUtil.isEmpty(warningSettingList)) {
            return Maps.newHashMap();
        }

        return CollectionUtils.toMap(warningSettingList, ProductWarningSettingDO::getId);
    }


}
