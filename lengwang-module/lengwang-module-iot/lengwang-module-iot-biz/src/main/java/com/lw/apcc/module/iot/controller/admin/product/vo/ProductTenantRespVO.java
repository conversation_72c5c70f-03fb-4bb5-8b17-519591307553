package com.lw.apcc.module.iot.controller.admin.product.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import java.util.*;
import com.alibaba.excel.annotation.*;

@Schema(description = "管理后台 - 产品租户关联信息 Response VO")
@Data
@ExcelIgnoreUnannotated
public class ProductTenantRespVO {

    @Schema(description = "主键ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "6067")
    @ExcelProperty("主键ID")
    private Long id;

    @Schema(description = "产品ID", example = "27307")
    @ExcelProperty("产品ID")
    private Long productId;

}