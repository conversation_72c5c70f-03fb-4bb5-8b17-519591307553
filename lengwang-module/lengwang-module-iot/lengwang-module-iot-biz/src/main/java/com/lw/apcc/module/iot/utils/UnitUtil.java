package com.lw.apcc.module.iot.utils;

import cn.hutool.core.util.ObjectUtil;
import com.google.common.collect.Lists;
import com.lw.apcc.common.security.SecurityUtils;
import com.lw.apcc.common.constants.IdConstants;

import java.util.List;

/**
 * <AUTHOR>
 */
public class UnitUtil {

    /**
     * 获取当前用户所属的经营主体
     * 如果没有则返回不存在的id
     *
     * @return 经营主体id
     */
    public static List<Long> getUnitIds() {
        List<Long> unitIdList = SecurityUtils.getUnitId();
        if (ObjectUtil.isEmpty(unitIdList)) {
            return Lists.newArrayList(IdConstants.NON_EXISTENT_ID);
        }
        return unitIdList;
    }


}
