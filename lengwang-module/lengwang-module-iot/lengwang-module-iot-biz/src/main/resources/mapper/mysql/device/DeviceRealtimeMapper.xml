<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lw.apcc.module.iot.dal.mapper.device.DeviceRealtimeMapper">

    <select id="getDeviceRealtimeOne" resultType="com.lw.apcc.module.iot.controller.admin.device.vo.DeviceRealtimeRespVO">
        <bind name="" value=""/>
        select
            dr.id,
            dr.device_id as deviceId,
            da.unit_id as unitId,
            da.unit_name as unitName,
            da.asset_id as assetId,
            da.asset_name as assetName,
            dr.time,
            dr.value,
            dr.note,
            dr.extend,
            dr.meta,
            dr.create_time
        from
            iot_device_realtime dr
            inner join iot_device_asset da on dr.device_id = da.device_id and da.deleted = 0
        where
            dr.deleted = 0
            and dr.id = ${id}
    </select>

    <select id="getDeviceRealtimeByDeviceId" resultType="com.lw.apcc.module.iot.controller.admin.device.vo.DeviceRealtimeRespVO">
        select
            dr.id,
            dr.device_id as deviceId,
            da.unit_id as unitId,
            da.unit_name as unitName,
            da.asset_id as assetId,
            da.asset_name as assetName,
            dr.time,
            dr.value,
            dr.note,
            dr.extend,
            dr.meta,
            dr.create_time
        from
            iot_device_realtime dr
            inner join iot_device_asset da on dr.device_id = da.device_id and da.deleted = 0
        where
            dr.deleted = 0
            and dr.device_id = #{deviceId}
    </select>

    <select id="getDeviceRealtimePage" resultType="com.lw.apcc.module.iot.controller.admin.device.vo.DeviceRealtimeRespVO">
        <include refid="sqlSelectByDeviceRealtime"></include>
    </select>

    <select id="getDeviceRealtimeList" resultType="com.lw.apcc.module.iot.controller.admin.device.vo.DeviceRealtimeRespVO">
        <include refid="sqlSelectByDeviceRealtime"></include>
    </select>

    <sql id="sqlSelectByDeviceRealtime">
        select
            dr.id,
            dr.device_id as deviceId,
            d.code as code,
            da.unit_id as unitId,
            da.unit_name as unitName,
            da.asset_id as assetId,
            da.asset_name as assetName,
            dr.time,
            dr.value,
            dr.note,
            dr.extend,
            dr.meta,
            dr.create_time
        from
            iot_device_realtime dr
            inner join iot_device_asset da on dr.device_id = da.device_id and da.deleted = 0
            inner join iot_device d on dr.device_id = d.id and d.deleted = 0
        where
            dr.deleted = 0
            <if test="pageReqVO.deviceId != null">
                and dr.device_id = ${pageReqVO.deviceId}
            </if>
            <if test="pageReqVO.code != null and pageReqVO.code != ''">
                and d.code like concat('%', #{pageReqVO.code}, '%')
            </if>
            <if test="pageReqVO.unitId != null">
                and da.unit_id = ${pageReqVO.unitId}
            </if>
            <if test="pageReqVO.assetId != null">
                and da.asset_id = ${pageReqVO.assetId}
            </if>
            <if test="pageReqVO.unitName != null and pageReqVO.unitName != ''">
                and da.unit_name like concat('%', #{pageReqVO.unitName}, '%')
            </if>
            <if test="pageReqVO.assetName != null and pageReqVO.assetName != ''">
                and da.asset_name like concat('%', #{pageReqVO.assetName}, '%')
            </if>
            <!-- 数据权限 -->
            <include refid="com.lw.apcc.module.iot.dal.mapper.device.DeviceAssetMapper.sqlWhereByDataPermission"></include>
    </sql>

    <delete id="removeByDeviceIds">
        delete
            dr
        from
            iot_device_realtime dr
            inner join iot_device d on dr.device_id = d.id and d.deleted = 0
        where
            d.deleted = 0
            <choose>
                <when test="deviceIds != null and deviceIds.size > 0">
                    and d.id in
                    <foreach collection="deviceIds" item="deviceId" open="(" separator="," close=")" index="index">
                        #{deviceId}
                    </foreach>
                </when>
                <otherwise>
                    and d.id = -1
                </otherwise>
            </choose>
    </delete>

    <insert id="saveBatchByRealtime">
        insert into iot_device_realtime (id, device_id, time, value, note, extend, meta, warning_type, warning_field, offline_status, offline_time, creator_id, create_time, updater_id, update_time)
        values
        <foreach collection="deviceRealtimeList" index="index" separator="," item="deviceRealtime">
            (
                #{deviceRealtime.id},
                #{deviceRealtime.deviceId},
                #{deviceRealtime.time},
                #{deviceRealtime.value},
                #{deviceRealtime.note},
                #{deviceRealtime.extend},
                #{deviceRealtime.meta},

                <choose>
                    <when test="deviceRealtime.warningType != null and deviceRealtime.warningType.size > 0">
                        <foreach collection="deviceRealtime.warningType.entrySet()" index="field"  item="type" separator="," open="'{" close="}'">"${field}":"${type}"</foreach>
                    </when>
                    <otherwise>null</otherwise>
                </choose>,
                #{deviceRealtime.warningField},
                #{deviceRealtime.offlineStatus},
                #{deviceRealtime.offlineTime},

                #{deviceRealtime.creatorId},
                #{deviceRealtime.createTime},
                #{deviceRealtime.updaterId},
                #{deviceRealtime.updateTime}
            )
        </foreach>
    </insert>

    <resultMap id="DeviceRealtimeWarningResultMap" type="com.lw.apcc.module.iot.controller.admin.device.vo.DeviceRealtimeWarningRespVO" autoMapping="true">
        <result column="warning_type" property="warningType"  javaType="java.util.Map" typeHandler="com.lw.apcc.framework.mybatis.core.type.StringMapTypeHandler" />
    </resultMap>

    <select id="listDeviceRealtimeWarning" resultMap="DeviceRealtimeWarningResultMap">
        select
            dr.id,
            dr.device_id,
            d.code device_code,
            d.name device_name,
            d.product_id,
            ar.unit_id,
            ur.name unit_name,
            ur.tenant_id,
            ar.id asset_id,
            ar.name asset_name,
            dr.time,
            dr.value,
            dr.note,
            dr.extend,
            dr.meta,
            dr.warning_type,
            dr.create_time,
            dr.offline_status
        from
            iot_device_realtime dr
            inner join iot_device d on dr.device_id = d.id and d.deleted = 0 and d.warning_status = 1
            inner join iot_warning w on d.id = w.device_id and w.deleted = 0 and w.status = ${@<EMAIL>()}
            inner join iot_asset_rel ar on ar.id = d.asset_id
            inner join iot_unit_rel ur on ur.id = ar.unit_id
        where
            dr.deleted = 0
        group by dr.id
    </select>

    <select id="listAssetDeviceRealtimeByAssetIds" resultType="com.lw.apcc.module.iot.api.device.dto.AssetDeviceRealtimeRespDTO">
        select
            d.asset_id,
            d.code device_code,
            s.slug sensor_slug,
            json_unquote(json_extract(dr.extend, concat('$.', s.slug))) value,
            dr.extend,
            dr.time,
            min(dr.offline_status) offline_status,
            max(json_unquote(json_extract(dr.warning_type, concat('$.', s.slug)))) warning_type_value
        from
            iot_device d
            inner join iot_device_realtime dr on d.id = dr.device_id and dr.deleted  = 0
            inner join iot_product_sensor ps on d.product_id = ps.product_id and ps.deleted = 0
            inner join iot_sensor s on s.id = ps.sensor_id and s.deleted = 0
        where
            d.deleted = 0
            <if test="assetIds != null and assetIds.size > 0">
                and d.asset_id in
                <foreach collection="assetIds" item="assetId" open="(" separator="," close=")" index="index">
                    ${assetId}
                </foreach>
            </if>
        group by d.asset_id, s.slug
    </select>

    <resultMap id="AssetDeviceRealtimeResultMap" type="com.lw.apcc.module.iot.controller.app.device.vo.AppAssetDeviceRealtimeRespVO" autoMapping="true">
        <result column="warning_range" property="warningRange"  javaType="com.alibaba.fastjson.JSONArray" typeHandler="com.lw.apcc.framework.mybatis.core.type.JSONArrayTypeHandler" />
    </resultMap>

    <select id="listAssetDeviceRealtime" resultMap="AssetDeviceRealtimeResultMap">
        select
            d.id,
            d.name,
            d.code,
            ifnull(json_unquote(json_extract(dr.extend, concat('$.', se.slug))), dr.value) value,
            dr.time,
            dr.extend,
            se.name sensor_name,
            se.slug sensor_slug,
            se.unit sensor_unit,
            ifnull(dr.offline_status, 1) offline_status,
            wr.warning_type warning_type_value,
            w.warning_range,
            w.status warning_status
        from
            iot_device d
            inner join iot_product_sensor ps on ps.product_id = d.product_id and ps.deleted = 0
            inner join iot_sensor se on se.id = ps.sensor_id and se.deleted = 0
            left join iot_device_realtime dr on dr.device_id = d.id and dr.deleted = 0
            left join iot_warning w on d.id = w.device_id and d.deleted = 0 and w.warning_field = se.slug and w.type != 0
            left join iot_warning_realtime wr on wr.warning_id = w.id and wr.deleted = 0
        where
            d.deleted = 0
            and d.asset_id = #{assetId}
            <if test="sensorSlugs != null and sensorSlugs.size > 0">
                <choose>
                    <when test="anyStatus != null and anyStatus == 2">
                        <foreach collection="sensorSlugs" open=" and ( " separator=" or " item="sensorSlug" index="index" close=")">
                            se.slug = #{sensorSlug}
                        </foreach>
                    </when>
                    <otherwise>
                        and se.slug in
                        <foreach collection="sensorSlugs" open="(" separator=", " item="sensorSlug" index="index" close=")">
                            #{sensorSlug}
                        </foreach>
                    </otherwise>
                </choose>
            </if>
    </select>

    <select id="listAssetDeviceRealtimeByProductId" resultMap="AssetDeviceRealtimeResultMap">
        select
            d.id,
            d.name,
            d.code,
            ifnull(json_unquote(json_extract(dr.extend, concat('$.', se.slug))), dr.value) value,
            dr.time,
            dr.extend,
            dr.meta,
            se.name sensor_name,
            se.slug sensor_slug,
            se.unit sensor_unit,
            ifnull(dr.offline_status, 1) offline_status,
            json_unquote(json_extract(dr.warning_type, concat('$.', se.slug))) warning_type_value,
            w.warning_range,
            w.status warning_status
        from
            iot_device d
            inner join iot_device_asset da on d.id = da.device_id and da.deleted = 0
            left join iot_product_sensor ps on ps.product_id = d.product_id and ps.deleted = 0
            left join iot_sensor se on se.id = ps.sensor_id and se.deleted = 0
            left join iot_device_realtime dr on dr.device_id = d.id and dr.deleted = 0
            left join iot_warning w on d.id = w.device_id and d.deleted = 0 and w.warning_field = se.slug and w.type != 0
        where
            d.deleted = 0
            and da.asset_id = #{assetId}
            and d.product_id = #{productId}
            and d.parent_id = 0
            <if test="name != null and name != ''">
                and (
                    d.name like concat('%', #{name}, '%')
                    or d.code like concat('%', #{name}, '%')
                )
            </if>
    </select>

    <select id="getDeviceRealtimeDetailsByDeviceId" resultMap="AssetDeviceRealtimeResultMap">
        select
            d.id,
            d.name,
            d.code,
            ifnull(json_unquote(json_extract(dr.extend, concat('$.', se.slug))), dr.value) value,
            dr.time,
            dr.extend,
            se.name sensor_name,
            se.slug sensor_slug,
            se.unit sensor_unit,
            ifnull(dr.offline_status, 1) offline_status,
    		json_unquote(json_extract(dr.warning_type, concat('$.', se.slug))) warning_type_value,
            w.warning_range,
            w.status warning_status
        from
            iot_device d
            inner join iot_device_asset da on d.id = da.device_id and da.deleted = 0
            left join iot_device_realtime dr on dr.device_id = d.id and dr.deleted = 0
            left join iot_product_sensor ps on ps.product_id = d.product_id and ps.deleted = 0
            left join iot_sensor se on se.id = ps.sensor_id and se.deleted = 0
            left join iot_warning w on d.id = w.device_id and d.deleted = 0 and w.warning_field = se.slug and w.type != 0
        where
            d.deleted = 0
            and d.id = #{deviceId}
    </select>

    <select id="listAssetDeviceWarningCountByAssetId" resultType="com.lw.apcc.module.iot.controller.app.device.vo.AppAssetDeviceWarningCountRespVO">
        select
            s.slug sensor_slug,
            s.name sensor_name,
            count(dr.id) warning_count
        from
            iot_device d
            inner join iot_device_asset da on d.id = da.device_id and da.deleted = 0
            inner join iot_device_realtime dr on dr.device_id = d.id and dr.deleted = 0
            inner join iot_warning w on d.id = w.device_id and d.deleted = 0 and w.status = 1
            inner join iot_warning_realtime wr on wr.warning_id = w.id and wr.deleted = 0
            left join iot_sensor s on s.slug = wr.warning_field and s.deleted = 0
        where
            d.deleted = 0
            and da.asset_id = ${assetId}
        group by s.id
    </select>

    <resultMap id="AssetDeviceWarningDetailsResultMap" type="com.lw.apcc.module.iot.controller.app.device.vo.AppAssetDeviceWarningDetailsRespVO" autoMapping="true">
        <result column="warning_range" property="warningRange"  javaType="com.alibaba.fastjson.JSONArray" typeHandler="com.lw.apcc.framework.mybatis.core.type.JSONArrayTypeHandler" />
    </resultMap>

    <select id="listAssetWarningDetailsByAssetId" resultMap="AssetDeviceWarningDetailsResultMap">
        select
            d.id,
            d.name,
            d.code,
            wr.warning_field,
            wr.warning_value,
            wr.warning_time,
            wr.warning_type,
            w.id warning_id,
            w.warning_range,
            w.status warning_status,
            ur.id unit_id,
            ur.name unit_name,
            ar.id asset_id,
            ar.name asset_name,
            ur.area_id
        from
            iot_warning_realtime wr
            inner join iot_warning w on w.id = wr.warning_id and w.deleted = 0
            inner join iot_device d on wr.device_id = d.id and d.deleted = 0
            inner join iot_asset_rel ar on ar.id = d.asset_id
            inner join iot_unit_rel ur on ur.id = ar.unit_id
        where
            wr.deleted = 0
            and d.asset_id = ${assetId}
            <if test="sensorSlug != null and sensorSlug != ''">
                and wr.warning_field = #{sensorSlug}
            </if>
        order by wr.warning_time desc
    </select>

    <update id="updateOfflineDevice">
        update
            iot_device_realtime dr
            inner join iot_device d on d.id = dr.device_id and d.deleted = 0 and d.warning_status = 1
            inner join iot_warning w on w.device_id = d.id and w.deleted = 0 and w.status = 1
            inner join iot_warning_type wt on w.warning_type_id = wt.id and wt.deleted = 0 and wt.type_code = '${@com.lw.apcc.module.iot.enums.warning.WarningTypeConstants@RULE_TYPE_CODE_CUSTOM_OFFLINE}'
        set
            dr.warning_type = '{"offline": 0}',
            dr.warning_field = 'offline',
            dr.offline_status = 0,
            dr.offline_time = ifnull(dr.offline_time, now())
        where
            dr.deleted = 0
            and dr.time &lt; date_sub(now(), interval ifnull(w.offline_minutes, ${offlineMinutes}) minute)
    </update>

    <select id="listOnlineDevice" resultType="java.lang.Long">
        select
            dr.id
        from
            iot_device_realtime dr
            inner join iot_device d on d.id = dr.device_id and d.deleted = 0 and d.warning_status = 1
            inner join iot_warning w on w.device_id = d.id and w.deleted = 0 and w.status = 1
            inner join iot_warning_type wt on w.warning_type_id = wt.id and wt.deleted = 0 and wt.type_code = '${@com.lw.apcc.module.iot.enums.warning.WarningTypeConstants@RULE_TYPE_CODE_CUSTOM_OFFLINE}'
        where
            dr.deleted = 0
            and dr.offline_status = 0
            and dr.time &gt;= date_sub(now(), interval ifnull(w.offline_minutes, ${offlineMinutes}) minute)
    </select>

</mapper>
