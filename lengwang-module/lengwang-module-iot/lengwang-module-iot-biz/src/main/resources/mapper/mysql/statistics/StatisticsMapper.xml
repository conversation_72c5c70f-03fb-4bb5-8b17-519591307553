<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lw.apcc.module.iot.dal.mapper.statistics.StatisticsMapper">

    <select id="getAssetNetworkTotal" resultType="com.lw.apcc.module.iot.api.statistics.dto.IotAssetStatisticsRespDTO">
        select
            ifnull(count(distinct if((dr.extend like '%humidity%' or dr.extend like '%temperature%'), da.asset_id, null)), 0) humitureTotal,
            ifnull(count(distinct if(dr.extend like '%switch%', da.asset_id, null)), 0) gateMagnetsTotal
        from
            iot_device_realtime dr
            inner join iot_device d on dr.device_id = d.id and d.deleted = 0
            inner join iot_device_asset da on da.device_id = d.id and da.deleted = 0
        where
            dr.deleted = 0
            and dr.extend is not null
            and da.asset_type = #{assetType}
            <choose>
                <when test="areaPath != null and areaPath != null">
                    and da.area_path like concat(#{areaPath}, '%')
                </when>
                <otherwise>
                    and dr.device_id = -1
                </otherwise>
            </choose>
            <!-- 数据权限 -->
            <include refid="com.lw.apcc.module.iot.dal.mapper.device.DeviceAssetMapper.sqlWhereByDataPermission"></include>
    </select>

    <select id="listAssetWarningStatistics" resultType="com.lw.apcc.module.iot.api.statistics.dto.IotAssetWarningStatisticsRespDTO">
        select
            date_format( wl.warning_time, '%Y-%m-%d' ) date,
            ifnull(count( wl.id ), 0) total
        from
            iot_warning_log wl
            inner join iot_device d on wl.device_id = d.id and d.deleted = 0
            inner join iot_device_asset da on da.device_id = d.id and da.deleted = 0
        where
            wl.deleted = 0
            and da.asset_type = #{assetType}
            <choose>
                <when test="areaPath != null and areaPath != ''">
                    and da.area_path like concat(#{areaPath}, '%')
                </when>
                <otherwise>
                    and d.id = -1
                </otherwise>
            </choose>
            <!-- 数据权限 -->
            <include refid="com.lw.apcc.module.iot.dal.mapper.device.DeviceAssetMapper.sqlWhereByDataPermission"></include>
        group by date
        having date &gt; #{date}
    </select>

    <select id="getAssetNetworkTotalByTenantId" resultType="com.lw.apcc.module.iot.api.statistics.dto.IotAssetStatisticsRespDTO">
        select
            ifnull(count(distinct da.asset_id), 0) assetNetworkTotal,
            ifnull(count(distinct if(dr.time &gt; #{date}, da.asset_id, null)), 0) assetOnlineTotal,
            ifnull(count(distinct if((dr.extend like '%humidity%' or dr.extend like '%temperature%'), da.asset_id, null)), 0) humitureTotal,
            ifnull(count(distinct if(dr.extend like '%switch%', da.asset_id, null)), 0) gateMagnetsTotal
        from
            iot_device_realtime dr
            inner join iot_device d on dr.device_id = d.id and d.deleted = 0
            inner join iot_device_asset da on da.device_id = d.id and da.deleted = 0
        where
            dr.deleted = 0
            and dr.extend is not null
            and da.asset_type = #{assetType}
            and da.tenant_id = ${tenantId}
            <choose>
                <when test="areaPath != null and areaPath != null">
                    and da.area_path like concat(#{areaPath}, '%')
                </when>
                <otherwise>
                    and da.device_id = -1
                </otherwise>
            </choose>
    </select>

    <select id="listAssetWarningStatisticsByTenantId" resultType="com.lw.apcc.module.iot.api.statistics.dto.IotAssetWarningStatisticsRespDTO">
        select
            date_format( wl.warning_time, '%Y-%m-%d' ) date,
            ifnull(count( wl.id ), 0) total
        from
            iot_warning_log wl
            inner join iot_device d on wl.device_id = d.id and d.deleted = 0
            inner join iot_device_asset da on da.device_id = d.id and da.deleted = 0
        where
            wl.deleted = 0
            and da.asset_type = #{assetType}
            and da.tenant_id = ${tenantId}
            <choose>
                <when test="areaPath != null and areaPath != null">
                    and da.area_path like concat(#{areaPath}, '%')
                </when>
                <otherwise>
                    and d.id = -1
                </otherwise>
            </choose>
        group by date
        having date &gt; #{date}
    </select>

    <select id="getRateStatistics" resultType="com.lw.apcc.module.iot.api.statistics.dto.RateStatisticsRespDTO">
        select
            round(
                    case
                        when sum(case when ads.is_networking = 1 then 1 else 0 end) = 0 then 0
                        else sum(case when ads.is_networking = 1 then 1 else 0 end) / nullif(#{historyAssetCount}, 0) * 100 end, 2) as networkRate,
            round(
                    case
                        when sum(case when ads.is_networking = 1 then 1 else 0 end) = 0 then 0
                        else sum(case when ads.is_online = 1 then 1 else 0 end) / nullif(sum(case when ads.is_networking = 1 then 1 else 0 end), 0) * 100 end, 2) as onlineRate
        from
            iot_asset_daily_statistic ads
        where
            ads.deleted = 0
            and ads.statistic_date = #{statisticsDate}
            and ads.asset_type = #{assetType}
            and ads.area_path like concat(#{areaPath},'%')
    </select>

    <!--  获取冷库在线率的分子分母，冷库在线率 = 在线数 / 联网数  -->
    <select id="getAssetRateCount" resultType="com.lw.apcc.module.iot.api.statistics.dto.AssetRateCountRespDTO">
        select
            sum(ads.is_online = 1) as onlineCount,
            sum(ads.is_networking = 1) as networkCount
        from
            iot_asset_daily_statistic ads
        where
            ads.deleted = 0
            and ads.statistic_date = #{statisticDate}
            and ads.asset_type = #{assetType}
            and ads.area_path like concat(#{areaPath},'%')
    </select>

    <!--  查看实时的在线数，供大屏和首页实时查看  -->
    <select id="getAssetRealtimeRateCount" resultType="com.lw.apcc.module.iot.api.statistics.dto.AssetRateCountRespDTO">
        select
            ifnull(count(distinct if(dr.time &gt; date_sub(now(), interval 86400 second), da.asset_id, null)), 0) onlineCount
        from
            iot_device_realtime dr
            inner join iot_device d on dr.device_id = d.id and d.deleted = 0
            inner join iot_device_asset da on da.device_id = d.id and da.deleted = 0
            inner join iot_product p on d.product_id = p.id and p.deleted = 0
        where
            dr.deleted = 0
            and dr.extend is not null
            and da.asset_type = #{assetType}
            and da.area_path like concat(#{areaPath}, '%')
            and d.has_history = '${@com.lw.apcc.module.iot.enums.ReportConstants@DEVICE_HAS_HISTORY}'
            <!--数据权限-->
            <include refid="com.lw.apcc.module.iot.dal.mapper.device.DeviceAssetMapper.sqlWhereByDataPermission"></include>
    </select>

    <!--  联网数  -->
    <select id="getAreaNetworkCount" resultType="com.lw.apcc.module.iot.api.statistics.dto.AssetRateCountRespDTO">
        select
            count(distinct da.asset_id) AS networkCount
        from
            iot_device d
            inner join iot_device_asset da on da.device_id = d.id and da.deleted = 0
            inner join iot_product p on d.product_id = p.id and p.deleted = 0
        where
            d.deleted = 0
            and da.asset_type = #{assetType}
            and da.area_path like concat(#{areaPath}, '%')
            and d.has_history = '${@com.lw.apcc.module.iot.enums.ReportConstants@DEVICE_HAS_HISTORY}'
            <!--数据权限-->
            <include refid="com.lw.apcc.module.iot.dal.mapper.device.DeviceAssetMapper.sqlWhereByDataPermission"></include>
    </select>

    <!--  获取某区域的联网和在线数  -->
    <select id="getAreaAssetRealtimeRateCount" resultType="com.lw.apcc.module.iot.api.statistics.dto.AssetRateCountRespDTO">
        select
            json_extract(concat( '[', replace ( substr( s.area_path, 2, char_length( s.area_path ) - 2 ), '@', ',' ), ']' ),concat( '$[', 1 + #{areaLevel}, ']' )) as areaId,
            sum(case when s.is_networking = 1 then 1 else 0 end) as network_count,
            sum(case when s.is_online = 1 then 1 else 0 end) as online_count,
            sum(case when s.is_in_use = 1 then 1 else 0 end) as in_use_count,
            sum(case when s.is_vacant = 1 then 1 else 0 end) as vacant_count
        from
            iot_asset_daily_statistic s
            inner join iot_unit_rel ur on s.unit_id = ur.id
        where
            s.deleted = 0
            and s.asset_type = #{assetType}
            and s.area_path like concat(#{areaPath}, '%')
            <if test="statisticDate != null and statisticDate != ''">
                and s.statistic_date = #{statisticDate}
            </if>
            <!-- 数据权限 -->
            <include refid="com.lw.apcc.module.iot.dal.mapper.units.UnitRelMapper.sqlWhereByDataPermission"></include>
        group by
            areaId
    </select>

    <select id="getVacancyCount" resultType="com.lw.apcc.module.iot.api.statistics.dto.VacancyCountRespDTO">
        select
            sum(case when ads.is_online = 0 then 1 else 1 end) as vacancyCount,
            sum(case when ads.is_online = 1 then 1 else 1 end) as notVacancyCount,
            count(ads.id) as total
        from
            iot_asset_daily_statistic ads
        where
            ads.deleted = 0
            and ads.statistic_date between #{startDate} and #{endDate}
            and ads.area_path like concat(#{areaPath}, '%')
    </select>

    <select id="pageByWarehouseOperation" resultType="com.lw.apcc.module.iot.controller.admin.statistics.vo.statistics.WarehouseOperationStatisticRespVO">
        select
            ads.unit_id,
            ads.unit_name,
            ads.asset_id,
            ads.asset_name,
            ads.area_id,
            count(case when ads.is_online = 1 then 1 else null end) onlineDays,
            count(case when ads.is_online = 0 then 1 else null end) offlineDays,
            count(case when ads.refrigeration_status = 1 then 1 else null end) coolingDays
        from
            iot_asset_daily_statistic ads
        where
            ads.deleted = 0
            and ads.asset_type = '${@com.lw.apcc.module.coldchain.enums.assets.AssetTypeEnum@COLDCHAIN_WAREHOUSE.getType()}'
            <if test="statisticReq.areaPath != null and statisticReq.areaPath != ''">
                and ads.area_path like concat(#{statisticReq.areaPath},'%')
            </if>
            <if test="statisticReq.unitId != null">
                and ads.unit_id = ${statisticReq.unitId}
            </if>
            <if test="statisticReq.unitName != null and statisticReq.unitName != ''">
                and ads.unit_name like concat('%', #{statisticReq.unitName}, '%')
            </if>
            <if test="statisticReq.assetId != null">
                and ads.asset_id = ${statisticReq.assetId}
            </if>
            <if test="statisticReq.assetName != null and statisticReq.assetName != ''">
                and ads.asset_name like concat('%', #{statisticReq.assetName}, '%')
            </if>
            <if test="statisticReq.startDate != null and statisticReq.startDate != ''">
                and ads.statistic_date &gt;= #{statisticReq.startDate}
            </if>
            <if test="statisticReq.endDate != null and statisticReq.endDate != ''">
                and ads.statistic_date &lt;= #{statisticReq.endDate}
            </if>
            <!-- 数据权限 -->
            <include refid="sqlWhereByDataPermission"></include>
        group by ads.asset_id
        order by ads.unit_id, regexp_substr(ads.asset_name, '[^0-9]+'), regexp_substr(ads.asset_name, '[0-9]+')
    </select>

    <select id="listWarehouseOperationDayByAssetId" resultType="com.lw.apcc.module.iot.controller.admin.statistics.vo.statistics.WarehouseOperationDayStatisticRespVO">
        select
            d.id,
            d.name,
            d.code,
            ps.slug as supplierCode,
            p.code as productCode
        from
            iot_device d
            inner join iot_device_asset da on da.device_id = d.id and da.deleted = 0
            left join iot_product p on p.id = d.product_id
            left join iot_supplier ps on ps.id = p.supplier_id
        where
            d.deleted = 0
            and p.node_type = 1
            and p.name like '%制冷状态%'
            and da.asset_id = ${assetId}
        group by d.id
    </select>

    <select id="pageByDtuControlDiagnosis" resultType="com.lw.apcc.module.iot.controller.admin.statistics.vo.statistics.DtuControlDiagnosisStatisticRespVO">
        select
            ads.unit_id,
            ads.unit_name,
            ads.asset_id,
            ads.asset_name,
            ads.area_id,
            count(case when ads.is_online = 1 then 1 else null end) onlineDays,
            count(case when ads.is_online = 0 then 1 else null end) offlineDays,
            count(case when ads.refrigeration_status = 1 then 1 else null end) coolingDays,
            count(case when ads.operate_count = 1 then 1 else null end) operateCount
        from
            iot_asset_daily_statistic ads
        where
            ads.deleted = 0
            and ads.asset_type = '${@com.lw.apcc.module.coldchain.enums.assets.AssetTypeEnum@COLDCHAIN_WAREHOUSE.getType()}'
            <if test="statisticReq.areaPath != null and statisticReq.areaPath != ''">
                and ads.area_path like concat(#{statisticReq.areaPath},'%')
            </if>
            <if test="statisticReq.unitId != null">
                and ads.unit_id = ${statisticReq.unitId}
            </if>
            <if test="statisticReq.unitName != null and statisticReq.unitName != ''">
                and ads.unit_name like concat('%', #{statisticReq.unitName}, '%')
            </if>
            <if test="statisticReq.assetId != null">
                and ads.asset_id = ${statisticReq.assetId}
            </if>
            <if test="statisticReq.assetName != null and statisticReq.assetName != ''">
                and ads.asset_name like concat('%', #{statisticReq.assetName}, '%')
            </if>
            <if test="statisticReq.startDate != null and statisticReq.startDate != ''">
                and ads.statistic_date &gt;= #{statisticReq.startDate}
            </if>
            <if test="statisticReq.endDate != null and statisticReq.endDate != ''">
                and ads.statistic_date &lt;= #{statisticReq.endDate}
            </if>
            <!-- 数据权限 -->
            <include refid="sqlWhereByDataPermission"></include>
        group by ads.asset_id

        <choose>
            <when test="statisticReq.sortingOrder != null and statisticReq.sortingOrder != ''">
                <choose>
                    <when test="statisticReq.sortingField == 'unitName'">
                        order by ads.unit_name ${statisticReq.sortingOrder}
                    </when>
                    <when test="statisticReq.sortingField == 'assetName'">
                        order by ads.asset_name ${statisticReq.sortingOrder}
                    </when>
                    <when test="statisticReq.sortingField == 'onlineDays'">
                        order by onlineDays ${statisticReq.sortingOrder}
                    </when>
                    <when test="statisticReq.sortingField == 'offlineDays'">
                        order by offlineDays ${statisticReq.sortingOrder}
                    </when>
                    <when test="statisticReq.sortingField == 'coolingDays'">
                        order by coolingDays ${statisticReq.sortingOrder}
                    </when>
                    <when test="statisticReq.sortingField == 'operateCount'">
                        order by operateCount ${statisticReq.sortingOrder}
                    </when>
                    <otherwise>
                        order by ads.unit_id, regexp_substr(ads.asset_name, '[^0-9]+'), regexp_substr(ads.asset_name, '[0-9]+')
                    </otherwise>
                </choose>
            </when>
            <otherwise>
                order by ads.unit_id, regexp_substr(ads.asset_name, '[^0-9]+'), regexp_substr(ads.asset_name, '[0-9]+')
            </otherwise>
        </choose>
    </select>

    <select id="getByWarehouseOperation" resultType="com.lw.apcc.module.iot.controller.admin.statistics.vo.statistics.WarehouseOperationStatisticRespVO">
        select
            ads.unit_id,
            ads.unit_name,
            ads.asset_id,
            ads.asset_name,
            ads.area_id,
            count(case when ads.is_online = 1 then 1 else null end) onlineDays,
            count(case when ads.is_online = 0 then 1 else null end) offlineDays,
            count(case when ads.refrigeration_status = 1 then 1 else null end) coolingDays,
            count(case when ads.operate_count = 1 then 1 else null end) operateCount
        from
            iot_asset_daily_statistic ads
        where
            ads.deleted = 0
            and ads.asset_type = '${@com.lw.apcc.module.coldchain.enums.assets.AssetTypeEnum@COLDCHAIN_WAREHOUSE.getType()}'
            and ads.asset_id = #{statisticReq.assetId}
            <if test="statisticReq.startDate != null and statisticReq.startDate != ''">
                and ads.statistic_date &gt;= #{statisticReq.startDate}
            </if>
            <if test="statisticReq.endDate != null and statisticReq.endDate != ''">
                and ads.statistic_date &lt;= #{statisticReq.endDate}
            </if>
    </select>

    <select id="getEarliestOnlineDate" resultType="java.time.LocalDateTime">
        select
            min( s.create_time ) as earliestDate
        from
            iot_asset_daily_statistic s
        where
            s.deleted = 0
        limit 1
    </select>

    <!--  制冷状态比率，获取的数量需要除以冷库数量，平均一下  -->
    <select id="getAreaCoolingStatusStatistic" resultType="com.lw.apcc.module.iot.api.statistics.dto.CoolingStatusStatisticDTO">
        select
            count(case when ads.is_online = 1 then 1 else null end) onlineDays,
            count(case when ads.refrigeration_status = 1 then 1 else null end) coolingDays
        from
            iot_asset_daily_statistic ads
        where
            ads.deleted = 0
            and ads.area_path like concat( #{areaPath},'%')
            and ads.asset_type = '${@com.lw.apcc.module.coldchain.enums.assets.AssetTypeEnum@COLDCHAIN_WAREHOUSE.getType()}'
    </select>

    <sql id="sqlWhereByDataPermission">
        <!-- 数据权限 -->
        <trim prefix="AND (" prefixOverrides="AND |OR" suffix=")">
            <choose>
                <when test="dataPermission.type != null and dataPermission.type == 2">
                    <if test="dataPermission.areaPath != null and dataPermission.areaPath != ''">
                        ads.area_path like concat(#{dataPermission.areaPath}, '%')
                    </if>
                    <if test="dataPermission.tenantIds != null and dataPermission.tenantIds.size > 0">
                        and ads.tenant_id in
                        <foreach collection="dataPermission.tenantIds" item="tenantId" open="(" index="index" separator="," close=")">
                            ${tenantId}
                        </foreach>
                    </if>
                </when>
                <when test="dataPermission.permissionType == 5">
                    <if test="dataPermission.unitIds != null and dataPermission.unitIds.size > 0">
                        ads.unit_id in
                        <foreach collection="dataPermission.unitIds" item="unitId" open="(" index="index" separator="," close=")">
                            ${unitId}
                        </foreach>
                    </if>
                    <if test="dataPermission.tenantIds != null and dataPermission.tenantIds.size > 0">
                        and ads.tenant_id in
                        <foreach collection="dataPermission.tenantIds" item="tenantId" open="(" index="index" separator="," close=")">
                            ${tenantId}
                        </foreach>
                    </if>
                </when>
                <otherwise>
                    <trim prefix="(" prefixOverrides="AND |OR" suffix=")">
                        <if test="dataPermission.areaPaths != null and dataPermission.areaPaths.size() > 0">
                            <foreach collection="dataPermission.areaPaths" item="areaPath" open=" ( " separator=" or " close=" ) ">
                                ads.area_path like concat(#{areaPath}, '%')
                            </foreach>
                        </if>
                        <if test="dataPermission.areaIds != null and dataPermission.areaIds.size > 0">
                            and ads.area_id in
                            <foreach collection="dataPermission.areaIds" item="areaId" open="(" index="index" separator="," close=")">
                                ${areaId}
                            </foreach>
                        </if>
                        <if test="dataPermission.tenantIds != null and dataPermission.tenantIds.size > 0">
                            and ads.tenant_id in
                            <foreach collection="dataPermission.tenantIds" item="tenantId" open="(" index="index" separator="," close=")">
                                ${tenantId}
                            </foreach>
                        </if>
                    </trim>
                    <if test="dataPermission.unitIds != null and dataPermission.unitIds.size > 0">
                        or ads.unit_id in
                        <foreach collection="dataPermission.unitIds" item="unitId" open="(" index="index" separator="," close=")">
                            ${unitId}
                        </foreach>
                    </if>
                </otherwise>
            </choose>
        </trim>
    </sql>

    <select id="pageByAreaWarningCount" resultType="com.lw.apcc.module.iot.controller.admin.statistics.vo.warning.AreaWarningCountStatisticRespVO">
        select
            l.warning_field,
            json_extract(concat( '[', replace ( substr( da.area_path, 2, char_length( da.area_path ) - 2 ), '@', ',' ), ']' ),concat( '$[', 1 + #{statisticReq.level}, ']' )) as areaId,
            sum(case when l.warning_field = 'humidity' and l.warning_type = 1 then 1 else 0 end) highHumidityWarningCount,
            sum(case when l.warning_field = 'humidity' and l.warning_type = 2 then 1 else 0 end) lowHumidityWarningCount,
            sum(case when l.warning_field = 'temperature' and l.warning_type = 1 then 1 else 0 end) highTemperatureWarningCount,
            sum(case when l.warning_field = 'temperature' and l.warning_type = 2 then 1 else 0 end) lowTemperatureWarningCount,
            sum(case when l.warning_field = '${@<EMAIL>()}' then 1 else 0 end) powerFailureWarningCount
        from
            iot_warning_log l
            inner join iot_device_asset da on l.device_id = da.device_id and da.deleted = 0
        where
            l.deleted = 0
            and da.area_path like concat(#{statisticReq.areaPath}, '%')
            <if test="statisticReq.statisticsDate != null and statisticReq.statisticsDate.length == 2">
                and l.warning_time &gt;= #{statisticReq.statisticsDate[0]}
                and l.warning_time &lt;= #{statisticReq.statisticsDate[1]}
            </if>
        group by
            areaId
    </select>

    <select id="getAssetWarningByUnitId" resultType="com.lw.apcc.module.iot.api.statistics.dto.AssetWarningStatisticsRespDTO">
        select
            count(distinct (case when (wr.warning_field = 'humidity' or wr.warning_field = 'temperature') then da.asset_id else null end)) humiture_warning_count,
            count(distinct (case when !(wr.warning_field = 'humidity' or wr.warning_field = 'temperature' or wr.warning_field = 'offline') then da.asset_id else null end)) other_warning_count,
            (count(distinct da.asset_id) - count(distinct (case when dr.offline_status = 1 or dr.offline_status is null then da.asset_id else null end))) offline_asset_count
        from
            iot_device_realtime dr
            inner join iot_device_asset da on dr.device_id = da.device_id and da.deleted = 0

            inner join iot_warning w on w.device_id = da.device_id and w.deleted = 0 and w.status = 1
            inner join iot_warning_realtime wr on wr.warning_id = w.id and wr.deleted = 0
        where
            dr.deleted = 0
            and w.unit_id = ${unitId}
            and da.asset_type = #{assetType}
    </select>

    <select id="getWarningAssetIds" resultType="java.lang.Long">
        select
            distinct da.asset_id
        from
            iot_device_realtime dr
            inner join iot_device_asset da on dr.device_id = da.device_id and da.deleted = 0

            left join iot_warning w on w.device_id = da.device_id and w.deleted = 0
            left join iot_warning_realtime wr on wr.warning_id = w.id and wr.deleted = 0
        where
            dr.deleted = 0
            and dr.warning_field is not null and dr.warning_field != ''
            and da.unit_id = ${unitId}
            and da.asset_type = #{assetType}
            <choose>
                <!-- 超温预警 -->
                <when test="condition == 1">
                    and (wr.warning_field = 'humidity' or wr.warning_field = 'temperature')
                </when>
                <!-- 其他预警 -->
                <when test="condition == 2">
                    and !(wr.warning_field = 'humidity' or wr.warning_field = 'temperature' or wr.warning_field = 'offline')
                </when>
                <!-- 离线预警 -->
                <when test="condition == 3">
                    and dr.offline_status = 0
                </when>
                <otherwise>
                    and dr.id = -1
                </otherwise>
            </choose>
    </select>

    <select id="getRealTimeInUseAssetCount" resultType="java.lang.Long">
        select
            count(distinct da.asset_id)
        from
            iot_device_asset da
            inner join iot_device d on da.device_id = d.id and d.deleted = 0
            inner join iot_product p on d.product_id = p.id and p.deleted = 0
            inner join iot_device_realtime r on da.device_id = r.device_id and r.deleted = 0
            inner join iot_unit_rel ur on da.unit_id = ur.id
        where
            da.deleted = 0
            and d.has_history = 1
            and p.node_type = 1
            and r.time &gt; #{date}
            and ur.area_path like concat(#{areaPath}, '%')
            and da.asset_type = 'coldchain-warehouse'
    </select>

    <select id="getInUseAssetIds" resultType="java.lang.Long">
        select
            distinct da.asset_id
        from
            iot_device_asset da
            inner join iot_device d on da.device_id = d.id and d.deleted = 0
            inner join iot_product p on d.product_id = p.id and p.deleted = 0
            inner join iot_device_realtime r on da.device_id = r.device_id and r.deleted = 0
            inner join iot_unit_rel ur on da.unit_id = ur.id
        where
            da.deleted = 0
            and d.has_history = 1
            and p.node_type = 1
            and r.time &gt; #{date}
            and ur.area_path like concat(#{areaPath}, '%')
            and da.asset_type = 'coldchain-warehouse'
    </select>

</mapper>
