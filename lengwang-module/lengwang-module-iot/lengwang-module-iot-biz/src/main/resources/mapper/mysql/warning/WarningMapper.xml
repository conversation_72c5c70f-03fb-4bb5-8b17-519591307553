<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lw.apcc.module.iot.dal.mapper.warning.WarningMapper">

    <select id="getWarningPage" resultType="com.lw.apcc.module.iot.controller.admin.warning.vo.WarningRespVO">
        <include refid="sqlSelectByWarning"/>
    </select>

    <select id="getWarningList" resultType="com.lw.apcc.module.iot.controller.admin.warning.vo.WarningRespVO">
        <include refid="sqlSelectByWarning"/>
    </select>

    <select id="getWarningSimpleList" resultType="com.lw.apcc.module.iot.controller.admin.warning.vo.WarningRespVO">
        <include refid="sqlSelectBySimpleWarning"/>
    </select>

    <resultMap id="WarningResultMap"  type="com.lw.apcc.module.iot.dal.dataobject.warning.WarningDO" autoMapping="true">
        <result column="action_ids" property="actionIds"  javaType="java.util.Set" typeHandler="com.lw.apcc.framework.mybatis.core.type.JsonLongSetTypeHandler" />
    </resultMap>

    <select id="listTaskWarning" resultMap="WarningResultMap">
        select
            w.id,
            w.type,
            w.name,
            w.unit_id,
            w.asset_id,
            w.device_id,
            w.warning_field,
            w.warning_range,
            w.delay_minutes,
            w.is_repeat,
            w.repeat_minutes,
            w.repeat_count,
            w.repeat_spacing_minutes,
            w.setting_user_id,
            w.setting_user_name,
            w.offline_minutes,
            w.action_ids,
            w.product_id,
            w.start_time,
            w.end_time,
            w.product_warning_setting_id,
            w.sensor_id,
            w.rule_type,
            w.warning_type_id
        from
            iot_warning w
            inner join iot_product_warning_setting pws on w.product_warning_setting_id = pws.id and pws.status = 1 and pws.deleted = 0
            inner join iot_device d on d.id = w.device_id and d.deleted = 0 and d.has_history = 1 and d.warning_status = 1
        where
            w.deleted = 0
            and w.status = ${@<EMAIL>()}
    </select>

    <sql id="sqlSelectByWarning">
        select
            w.*,
            d.name as deviceName,
            d.code
        from
            iot_warning w
            left join iot_device d on w.device_id = d.id and d.deleted = 0
            left join iot_device_asset da on (da.device_id = w.device_id or (w.asset_id = da.asset_id and w.device_id is null) or (w.unit_id = da.unit_id and w.asset_id is null and w.device_id is null)) and da.deleted = 0
        where
            w.deleted = 0
            <if test="pageReqVO.assetType != null and pageReqVO.assetType != ''">
                and da.asset_type = #{pageReqVO.assetType}
            </if>
            <if test="pageReqVO.unitName != null and pageReqVO.unitName != ''">
                and da.unit_name like concat('%', #{pageReqVO.unitName}, '%')
            </if>
            <if test="pageReqVO.name != null and pageReqVO.name != ''">
                and w.name like concat('%', #{pageReqVO.name}, '%')
            </if>
            <if test="pageReqVO.unitId != null and pageReqVO.unitId != ''">
                and da.unit_id = #{pageReqVO.unitId}
            </if>
            <if test="pageReqVO.unitIds != null and pageReqVO.unitIds.size > 0">
                and da.unit_id in
                <foreach collection="pageReqVO.unitIds" item="unitId" open="(" index="index" separator="," close=")">
                    ${unitId}
                </foreach>
            </if>
            <choose>
                <when test="pageReqVO.warningLevel == 1">
                    and da.device_id = w.device_id
                </when>
                <when test="pageReqVO.warningLevel == 2">
                    and w.asset_id = da.asset_id and w.device_id is null
                </when>
                <when test="pageReqVO.warningLevel == 3">
                    and w.unit_id = da.unit_id and w.asset_id is null and w.device_id is null
                </when>
            </choose>
            <!-- 数据权限 -->
            <include refid="com.lw.apcc.module.iot.dal.mapper.device.DeviceAssetMapper.sqlWhereByDataPermission"></include>
        group by w.id
        <choose>
            <when test="pageReqVO.sortingOrder != null and pageReqVO.sortingOrder != ''">
                <choose>
                    <when test="pageReqVO.sortingField == 'deviceName'">
                        order by deviceName ${pageReqVO.sortingOrder}
                    </when>
                    <otherwise>
                        order by w.create_time desc
                    </otherwise>
                </choose>
            </when>
            <otherwise>
                order by w.create_time desc
            </otherwise>
        </choose>
    </sql>

    <sql id="sqlSelectBySimpleWarning">
        select
            w.id,
            w.name
        from
            iot_warning w
            left join iot_warning_default wd on w.default_id = wd.id and wd.deleted = 0
            inner join iot_device d on w.device_id = d.id and d.deleted = 0
        where
            w.deleted = 0
            <if test="pageReqVO.name != null and pageReqVO.name != ''">
                and w.name like concat('%', #{pageReqVO.name}, '%')
            </if>
    </sql>

    <select id="getDeviceWarningRange" resultType="com.lw.apcc.module.iot.controller.admin.warning.vo.DeviceWarningRangeRespVO">
        select
            w.id,
            w.warning_range as warningRange,
            w.warning_field as warningField,
            w.status
        from
            iot_warning w
        where
            w.deleted = 0
            and w.device_id = #{deviceId}
    </select>

    <resultMap id="DeviceWarningDetailsResultMap" type="com.lw.apcc.module.iot.controller.app.warning.vo.AppDeviceSensorWarningDetailsRespVO" autoMapping="true">
        <result column="warning_range" property="warningRange"  javaType="com.alibaba.fastjson.JSONArray" typeHandler="com.lw.apcc.framework.mybatis.core.type.JSONArrayTypeHandler" />
    </resultMap>

    <select id="listByDeviceWarningDetails" resultMap="DeviceWarningDetailsResultMap">
        select
            w.id,
            w.device_id,
            w.warning_field,
            w.warning_range,
            w.type,
            w.status
        from
            iot_device d
            inner join iot_device_asset da on da.device_id = d.id and da.deleted = 0
            inner join iot_warning w on w.device_id = d.id and w.deleted = 0
        where
            d.deleted =  0
            and da.asset_id = #{assetId}
        order by d.name
    </select>

    <select id="listDeviceWarningDetailsByDeviceId" resultMap="DeviceWarningDetailsResultMap">
        select
            w.id,
            w.device_id,
            w.warning_field,
            w.warning_range,
            w.type,
            w.status
        from
            iot_device d
            inner join iot_device_asset da on da.device_id = d.id and da.deleted = 0
            inner join iot_warning w on w.device_id = d.id and w.deleted = 0
        where
            d.deleted =  0
            and d.id = #{deviceId}
        order by d.name
    </select>

    <select id="getDeviceWarningDetailsByDeviceId" resultType="com.lw.apcc.module.iot.controller.app.warning.vo.AppDeviceWarningDetailsRespVO">
        select
            d.id device_id,
            d.name device_name,
            d.code device_code,
            d.product_id,
            d.warning_status,
            da.asset_id,
            da.asset_name
        from
            iot_device d
            inner join iot_device_asset da on da.device_id = d.id and da.deleted = 0
        where
            d.deleted =  0
            and d.id = #{deviceId}
    </select>

    <select id="getOfflineWarningByAssetId" resultType="com.lw.apcc.module.iot.dal.dataobject.warning.WarningDO">
        select
            w.id,
            w.offline_minutes,
            w.status
        from
            iot_warning w
        where
            w.deleted = 0
            and w.type = 0
            and w.device_id is null
            and w.asset_id = #{assetId}
    </select>

    <select id="getGraphWarningSettingList" resultType="com.lw.apcc.module.iot.controller.admin.unitPositionGraph.vo.AssetDeviceWarningSettingRespVO">
        select
            w.device_id,
            case
                when w.id is null or w.status = 0 then false
                else true
                end as openWarning,
            w.warning_field,
            wr.warning_type,
            w.warning_range,
            wr.warning_value
        from
            iot_warning w
            left join iot_warning_realtime wr on wr.warning_id = w.id and wr.deleted = 0
        where
            w.deleted = 0
            <choose>
                <when test="deviceIds != null and deviceIds.size > 0">
                    and w.device_id in
                    <foreach collection="deviceIds" item="deviceId" open="(" close=")" separator=",">
                        #{deviceId}
                    </foreach>
                </when>
                <otherwise>
                    and w.id = -1
                </otherwise>
            </choose>
    </select>

    <resultMap id="WarningRealtimeCloseResultMap" type="com.lw.apcc.module.iot.controller.admin.warning.vo.WarningRealtimeCloseRespVO" autoMapping="true">
        <result column="warning_type" property="warningType"  javaType="java.util.Map" typeHandler="com.lw.apcc.framework.mybatis.core.type.StringMapTypeHandler" />
    </resultMap>

    <select id="listWarningRealtimeByClose" resultMap="WarningRealtimeCloseResultMap">
        select
            dr.id device_realtime_id,
            wr.warning_field,
            dr.warning_type
        from
            iot_warning w
            inner join iot_warning_realtime wr on wr.warning_id = w.id and wr.deleted = 0
            inner join iot_device_realtime dr on dr.device_id = wr.device_id and dr.deleted = 0 and ((w.rule_type = 2 and dr.warning_type like concat('%"', wr.warning_field, '":"',wr.warning_type,'"%')) or (w.rule_type = 1 and dr.warning_type like concat('%"', wr.warning_field, '"%')))
        where
            w.deleted = 0
            and w.status = 0
    </select>

</mapper>
