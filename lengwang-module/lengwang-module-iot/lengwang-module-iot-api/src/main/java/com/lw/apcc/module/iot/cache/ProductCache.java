package com.lw.apcc.module.iot.cache;

import com.lw.apcc.common.util.object.ObjectUtil;
import com.lw.apcc.common.util.spring.SpringUtil;
import com.lw.apcc.framework.redis.core.cache.BaseCache;
import com.lw.apcc.framework.redis.utils.RedisCacheUtil;
import com.lw.apcc.module.iot.api.product.ProductApi;
import com.lw.apcc.module.iot.api.product.dto.ProductRespDTO;
import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR> yang
 * @description 产品缓存
 * @date 2024/6/26 11:23
 */
public class ProductCache implements BaseCache {

    private static final String CACHE_NAME = BASE_CACHE + "product";
    private static final String CACHE_NAME_CODE = "name_code:%s_%s";
    private static final String CACHE_ID = "id:%s";

    /**
     * API 服务
     */
    private static ProductApi productApi;

    static {
        productApi = SpringUtil.getBean(ProductApi.class);
    }

    public static void clear() {
        RedisCacheUtil.clear(CACHE_NAME);
        ProductTenantCache.clear();
    }

    /**
     * @description 根据slug查询传感器
     * <AUTHOR> yang
     * @date 2024/2/21 15:14
     */
    public static ProductRespDTO getByNameAndCode(String name, String code) {
        if (StringUtils.isAnyEmpty(name, code)) {
            return null;
        }
        return RedisCacheUtil.get(
                CACHE_NAME,
                String.format(CACHE_NAME_CODE, name, code),
                () -> productApi.getByNameAndCode(name, code).getCheckedData()
        );
    }

    /**
     * 根据主键id查询产品信息
     *
     * @param id
     * @return
     */
    public static ProductRespDTO getById(Long id) {
        if (id == null) {
            return null;
        }
        return RedisCacheUtil.get(
                CACHE_NAME,
                String.format(CACHE_ID, id),
                () -> productApi.getById(id).getCheckedData()
        );
    }

    /**
     * 根据主键id查询产品信息
     *
     * @param id
     * @return
     */
    public static String getNameById(Long id) {
        ProductRespDTO product = getById(id);
        if(product != null){
            return product.getName();
        }
        return "";
    }

    /**
     * 对比产品类型是否相同
     *
     * @param id
     * @return
     */
    public static boolean isAnyType(Long id, String type, String... types) {
        ProductRespDTO respDTO = getById(id);
        if (respDTO != null) {
            if (type.equals(respDTO.getType())) {
                return true;
            }
            if (ObjectUtil.isNotEmpty(types)) {
                for (String productType : types) {
                    if (respDTO.getType().equals(productType)) {
                        return true;
                    }
                }
            }
        }
        return false;
    }


}
