package com.lw.apcc.module.iot.api.product;

import com.lw.apcc.common.pojo.CommonResult;
import com.lw.apcc.module.iot.enums.ApiConstants;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.Set;


/**
 * 物联网产品租户关联 API 接口
 *
 * <AUTHOR>
 */
@FeignClient(name = ApiConstants.NAME)
@Tag(name = "RPC 服务 - 物联网产品租户关联 API")
public interface ProductTenantApi {

    String PREFIX = ApiConstants.PREFIX + "/product";

    @GetMapping(PREFIX + "/get-product-ids-by-tenant-id")
    @Parameter(name = "tenantId", description = "租户ID", example = "租户ID")
    @Operation(summary = "根据租户ID查询产品ID")
    CommonResult<Set<Long>> getProductIdsByTenantId(@RequestParam("tenantId") Long tenantId);

}
