package com.lw.apcc.module.iot.cache;

import com.google.common.collect.Sets;
import com.lw.apcc.common.security.SecurityUtils;
import com.lw.apcc.common.util.object.ObjectUtil;
import com.lw.apcc.common.util.spring.SpringUtil;
import com.lw.apcc.framework.redis.core.cache.BaseCache;
import com.lw.apcc.framework.redis.utils.RedisCacheUtil;
import com.lw.apcc.module.iot.api.product.ProductTenantApi;

import java.util.Set;

import static com.lw.apcc.common.constants.IdConstants.NON_EXISTENT_ID;

/**
 * <AUTHOR> yang
 * @description 产品租户缓存
 * @date 2024/6/26 11:23
 */
public class ProductTenantCache implements BaseCache {

    private static final String CACHE_NAME = BASE_CACHE + "product_tenant";
    private static final String CACHE_TENANT_ID = "tenant_id:%s";

    /**
     * API 服务
     */
    private static ProductTenantApi productTenantApi;

    static {
        productTenantApi = SpringUtil.getBean(ProductTenantApi.class);
    }

    /**
     * 清除缓存
     *
     */
    public static void clear() {
        RedisCacheUtil.clear(CACHE_NAME);
    }

    /**
     * 清除缓存
     *
     * @param tenantId
     */
    public static void clear(Long tenantId) {
        RedisCacheUtil.clear(CACHE_NAME, String.format(CACHE_TENANT_ID, tenantId));
    }

    /**
     * 根据查询产品ID
     *
     * @return
     */
    public static Set<Long> getProductIds() {
        return getProductIdsByTenantId(SecurityUtils.getTenantId());
    }

    /**
     * 根据租户ID查询产品ID
     *
     * @param tenantId
     * @return
     */
    public static Set<Long> getProductIdsByTenantId(Long tenantId) {
        if (tenantId == null) {
            return null;
        }
        return RedisCacheUtil.get(
                CACHE_NAME,
                String.format(CACHE_TENANT_ID, tenantId),
                () -> {
                    Set<Long> productIds = productTenantApi.getProductIdsByTenantId(tenantId).getData();
                    if (ObjectUtil.isEmpty(productIds)) {
                        return Sets.newHashSet(NON_EXISTENT_ID);
                    }
                    return productIds;
                }
        );
    }

}
