package com.lw.apcc.module.system.service.permission.data;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.lw.apcc.common.util.collection.CollectionUtils;
import com.lw.apcc.common.constants.IdConstants;
import com.lw.apcc.framework.mybatis.core.service.BaseServiceImpl;
import com.lw.apcc.framework.tenant.core.util.TenantUtils;
import com.lw.apcc.module.system.api.permission.dto.TenantDataPermissionRespDTO;
import com.lw.apcc.module.system.api.tenant.dto.TenantRespDTO;
import com.lw.apcc.module.system.cache.TenantCache;
import com.lw.apcc.module.system.cache.TenantDataPermissionCache;
import com.lw.apcc.module.system.constants.AreaConstants;
import com.lw.apcc.module.system.controller.admin.permission.vo.data.TenantDataPermissionCustomReqVO;
import com.lw.apcc.module.system.controller.admin.permission.vo.data.TenantDataPermissionCustomRespVO;
import com.lw.apcc.module.system.controller.admin.permission.vo.data.TenantDataPermissionRespVO;
import com.lw.apcc.module.system.controller.admin.permission.vo.data.TenantDataPermissionSaveReqVO;
import com.lw.apcc.module.system.convert.permission.TenantDataPermissionConvert;
import com.lw.apcc.module.system.convert.permission.TenantDataPermissionCustomConvert;
import com.lw.apcc.module.system.dal.dataobject.permission.data.DataPermissionDO;
import com.lw.apcc.module.system.dal.dataobject.permission.data.TenantDataPermissionCustomDO;
import com.lw.apcc.module.system.dal.dataobject.permission.data.TenantDataPermissionDO;
import com.lw.apcc.module.system.dal.mapper.permission.data.TenantDataPermissionMapper;
import com.lw.apcc.module.system.dal.mapper.tenant.TenantMapper;
import com.lw.apcc.module.system.enums.permission.DataPermissionTypeEnum;
import com.lw.apcc.module.system.enums.permission.TenantDataPermissionCustomTypeEnum;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 租户数据权限信息 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class TenantDataPermissionServiceImpl extends BaseServiceImpl<TenantDataPermissionMapper, TenantDataPermissionDO> implements TenantDataPermissionService {


    @Resource
    private DataPermissionService dataPermissionService;

    @Resource
    private TenantDataPermissionCustomService customService;

    @Resource
    private TenantMapper tenantMapper;

    /**
     * 根据租户id查询租户绑定权限
     *
     * @param tenantId
     * @return
     */
    @Override
    public Map<Long, TenantDataPermissionRespVO> getByTenantId(Long tenantId) {
        // 数据权限
        List<TenantDataPermissionRespVO> dataPermissionList = baseMapper.listByTenantId(tenantId);

        // 自定义数据权限
        Map<Long, List<TenantDataPermissionCustomRespVO>> permissionCustomMap = customService.getDataPermissionCustomByTenantId(tenantId);

        dataPermissionList.forEach(dataPermission -> {
            if (dataPermission.getId() != null && DataPermissionTypeEnum.CUSTOM.getType().equals(dataPermission.getPermissionType())) {
                dataPermission.setCustomList(permissionCustomMap.get(dataPermission.getId()));
            }
        });

        return CollectionUtils.convertMap(dataPermissionList, TenantDataPermissionRespVO::getDataPermissionId);
    }

    /**
     * 保存租户数据权限
     *
     * @param saveReqList
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public Boolean saveTenantDataPermission(Long tenantId, List<TenantDataPermissionSaveReqVO> saveReqList) {
        // 删除数据权限
        removeTenantDataPermission(tenantId);

        // 转换加载
        List<TenantDataPermissionDO> permissionList = Lists.newArrayList();
        List<TenantDataPermissionCustomDO> permissionCustomList = Lists.newArrayList();

        initTenantDataPermission(permissionList, permissionCustomList, tenantId, saveReqList);

        // 保存数据权限
        baseMapper.insertBatch(permissionList);
        customService.saveBatch(permissionCustomList);

        // 清除权限缓存
        TenantDataPermissionCache.clear();
        return true;
    }

    /**
     * 根据租户code查询租户绑定权限
     *
     * @param code
     * @param tenantId
     * @return
     */
    @Override
    public TenantDataPermissionRespDTO getDataPermissionByCode(String code, Long tenantId) {
        // ============================================================================
        // 数据权限
        DataPermissionDO dataPermission = dataPermissionService.getByCode(code);
        if (dataPermission == null) {
            return buildNoPermission();
        }
        // 租户绑定数据权限
        TenantDataPermissionDO tenantDataPermission = getDataPermissionByDataPermissionId(dataPermission.getId(), tenantId);

        // ============================================================================
        // 绑定权限类型
        String permissionType = dataPermission.getDefaultType();
        if (tenantDataPermission != null) {
            permissionType = tenantDataPermission.getPermissionType();
        }

        // 未获取到权限类型 -- 无权限
        if (StrUtil.isEmpty(permissionType)) {
            return buildNoPermission();
        }

        // ============================================================================
        TenantRespDTO tenantResp = TenantCache.getById(tenantId);

        // 全部
        if (DataPermissionTypeEnum.ALL.equalsType(permissionType)) {
            return buildByAll(DataPermissionTypeEnum.ALL, tenantResp);
        }

        // 下属区域
        if (DataPermissionTypeEnum.SUBORDINATE_REGIONS.equalsType(permissionType)) {
            return buildBySubordinateRegions(DataPermissionTypeEnum.SUBORDINATE_REGIONS, tenantResp);
        }

        // 仅查看本区域
        if (DataPermissionTypeEnum.THIS_REGION.equalsType(permissionType)) {
            return buildByThisRegion(DataPermissionTypeEnum.THIS_REGION, tenantResp);
        }

        // 自定义设置
        if (DataPermissionTypeEnum.CUSTOM.equalsType(permissionType)) {
            return buildByCustom(DataPermissionTypeEnum.CUSTOM, tenantDataPermission);
        }

        // 仅查看租户
        if (DataPermissionTypeEnum.ONLY_ONESELF.equalsType(permissionType)) {
            return buildByOnlyOneself(DataPermissionTypeEnum.ONLY_ONESELF, tenantResp);
        }

        // 无权限查看
        return buildNoPermission();
    }

    /**
     * 全部
     *
     * @param permissionType
     * @return
     */
    private TenantDataPermissionRespDTO buildByAll(DataPermissionTypeEnum permissionType, TenantRespDTO tenantResp) {
        TenantDataPermissionRespDTO permissionRespDTO = buildPermission(permissionType);

        // 顶级平台租户，可以查看所有
        if (TenantUtils.isSystemTenant(tenantResp.getId())) {
            return permissionRespDTO;
        }

        // 租户ID
        Set<Long> tenantIds = tenantMapper.listOpenIds();
        tenantIds.add(tenantResp.getId());
        permissionRespDTO.setTenantIds(tenantIds);

        // 区域PATH
        permissionRespDTO.setAreaPath(AreaConstants.PATH_ROOT);

        return permissionRespDTO;
    }

    /**
     * 仅查看租户
     *
     * @param permissionType
     * @param tenantResp
     * @return
     */
    private TenantDataPermissionRespDTO buildByOnlyOneself(DataPermissionTypeEnum permissionType, TenantRespDTO tenantResp) {
        TenantDataPermissionRespDTO permissionRespDTO = buildPermission(permissionType);

        // 区域ID
        permissionRespDTO.setAreaPath(tenantResp.getAreaPath());

        // 租户ID
        permissionRespDTO.setTenantIds(Sets.newHashSet(tenantResp.getId()));

        // 经营主体ID使用区域和租户筛选

        return permissionRespDTO;
    }

    /**
     * 根据数据权限ID查询租户绑定权限
     *
     * @param dataPermissionId
     * @param tenantId
     * @return
     */
    private TenantDataPermissionDO getDataPermissionByDataPermissionId(Long dataPermissionId, Long tenantId) {
        List<TenantDataPermissionDO> dataPermissionList = baseMapper.listByDataPermissionId(dataPermissionId, tenantId);
        if (ObjectUtil.isNotEmpty(dataPermissionList)) {
            return dataPermissionList.get(0);
        }
        return null;
    }

    /**
     * 自定义设置
     *
     * @param permissionType
     * @param tenantDataPermission
     * @return
     */
    private TenantDataPermissionRespDTO buildByCustom(DataPermissionTypeEnum permissionType, TenantDataPermissionDO tenantDataPermission) {
        TenantDataPermissionRespDTO permissionRespDTO = buildPermission(permissionType);
        if (tenantDataPermission == null) {
            return permissionRespDTO;
        }
        // 区域ID
        Set<String> areaPaths = customService.getCustomPathsByCustomType(tenantDataPermission.getId(), TenantDataPermissionCustomTypeEnum.AREA);
        permissionRespDTO.setAreaPaths(areaPaths);

        // 租户ID
        Set<Long> tenantIds = getCustomTenantIds(tenantDataPermission.getId(), areaPaths);
        permissionRespDTO.setTenantIds(tenantIds);

        // 经营主体ID
        // 经营主体id只包含自定义，如果经营主体ID集合和别的筛选ID集合同时存在 则使用或的关系筛选经营主体，防止筛除掉新增的经营主体
        Set<Long> unitsIds = customService.getCustomIdsByCustomType(tenantDataPermission.getId(), TenantDataPermissionCustomTypeEnum.UNITS);
        permissionRespDTO.setUnitIds(unitsIds);

        return permissionRespDTO;
    }

    /**
     * 自定义设置租户ID
     *
     * @param tenantDataPermissionId
     * @param areaPaths
     * @return
     */
    private Set<Long> getCustomTenantIds(Long tenantDataPermissionId, Set<String> areaPaths) {
        Set<Long> tenantIds = customService.getCustomIdsByCustomType(tenantDataPermissionId, TenantDataPermissionCustomTypeEnum.TENANT);

        Set<Long> areaTenantIds = tenantMapper.getIdsByAreaPaths(areaPaths);
        if (ObjectUtil.isNotEmpty(areaTenantIds)) {
            tenantIds.addAll(areaTenantIds);
        }
        return tenantIds;
    }

    /**
     * 仅查看本区域
     *
     * @param permissionType
     * @param tenantResp
     * @return
     */
    private TenantDataPermissionRespDTO buildByThisRegion(DataPermissionTypeEnum permissionType, TenantRespDTO tenantResp) {
        TenantDataPermissionRespDTO permissionRespDTO = buildPermission(permissionType);

        // 区域ID
        permissionRespDTO.setAreaPath(tenantResp.getAreaPath());

        // 租户ID
        Set<Long> tenantIds = tenantMapper.getIdsByAreaIds(Sets.newHashSet(tenantResp.getAreaId()));
        tenantIds.add(tenantResp.getId());
        permissionRespDTO.setTenantIds(tenantIds);

        // 经营主体，仅查看自己的时候不设置经营主体范围，使用区域ID 或者 租户ID 查询出租户下所有信息
        return permissionRespDTO;
    }

    /**
     * 设定下属区域权限下属区域
     *
     * @param permissionType
     * @return
     */
    private TenantDataPermissionRespDTO buildBySubordinateRegions(DataPermissionTypeEnum permissionType, TenantRespDTO tenantResp) {
        TenantDataPermissionRespDTO permissionRespDTO = buildPermission(permissionType);

        // 区域PATH
        permissionRespDTO.setAreaPath(tenantResp.getAreaPath());

        // 租户ID
        Set<Long> tenantIds = tenantMapper.getIdsByAreaPath(tenantResp.getAreaPath());
        tenantIds.add(tenantResp.getId());
        permissionRespDTO.setTenantIds(tenantIds);

        // 下属区域不设置以下ID集合，直接使用PATH获取区域ID
        // 经营主体ID
        // 区域ID
        return permissionRespDTO;
    }

    /**
     * 指定权限类型
     *
     * @return
     */
    private TenantDataPermissionRespDTO buildPermission(DataPermissionTypeEnum permissionType) {
        TenantDataPermissionRespDTO respDTO = new TenantDataPermissionRespDTO()
                .setAll(DataPermissionTypeEnum.ALL.equalsType(permissionType.getType()))
                .setPermissionType(permissionType.getType())
                .setType(permissionType.getResultType().getType())
                .setAreaIds(Sets.newHashSet())
                .setAreaPath("")
                .setAreaPaths(Sets.newHashSet())
                .setTenantIds(Sets.newHashSet())
                .setUnitIds(Sets.newHashSet());
        return respDTO;
    }

    /**
     * 无权限
     *
     * @return
     */
    private TenantDataPermissionRespDTO buildNoPermission() {
        TenantDataPermissionRespDTO respDTO = new TenantDataPermissionRespDTO()
                .setAll(false)
                .setPermissionType(DataPermissionTypeEnum.NO.getType())

                .setType(DataPermissionTypeEnum.NO.getResultType().getType())

                .setAreaPath(String.valueOf(IdConstants.NON_EXISTENT_ID))
                .setAreaPaths(Sets.newHashSet(String.valueOf(IdConstants.NON_EXISTENT_ID)))
                .setAreaIds(Sets.newHashSet(IdConstants.NON_EXISTENT_ID))

                .setTenantIds(Sets.newHashSet(IdConstants.NON_EXISTENT_ID))

                .setUnitIds(Sets.newHashSet(IdConstants.NON_EXISTENT_ID));
        return respDTO;
    }

    /**
     * 转换加载数据权限
     *
     * @param tenantPermissionList
     * @param tenantPermissionCustomList
     * @param tenantId
     * @param saveReqList
     */
    private void initTenantDataPermission(List<TenantDataPermissionDO> tenantPermissionList, List<TenantDataPermissionCustomDO> tenantPermissionCustomList, Long tenantId, List<TenantDataPermissionSaveReqVO> saveReqList) {
        saveReqList.forEach(saveReq -> {
            // 数据权限
            Long tenantDataPermissionId = IdWorker.getId();

            TenantDataPermissionDO dataPermission = TenantDataPermissionConvert.INSTANCE.convert(saveReq);
            dataPermission.setId(tenantDataPermissionId);
            dataPermission.setTenantId(tenantId);
            tenantPermissionList.add(dataPermission);

            // 自定义数据权限
            List<TenantDataPermissionCustomReqVO> permissionCustomList = saveReq.getPermissionCustomList();
            if (ObjectUtil.isNotEmpty(permissionCustomList)) {
                permissionCustomList.forEach(permissionCustom -> {
                    TenantDataPermissionCustomDO dataPermissionCustom = TenantDataPermissionCustomConvert.INSTANCE.convert(permissionCustom);

                    dataPermissionCustom.setTenantId(tenantId);
                    dataPermissionCustom.setTenantDataPermissionId(tenantDataPermissionId);

                    tenantPermissionCustomList.add(dataPermissionCustom);
                });
            }
        });
    }

    /**
     * 删除数据权限
     *
     * @param tenantId
     */
    private void removeTenantDataPermission(Long tenantId) {
        // 删除
        baseMapper.removeByTenantId(tenantId);
        // 删除自定义数据权限
        customService.removeByTenantId(tenantId);
    }

}
