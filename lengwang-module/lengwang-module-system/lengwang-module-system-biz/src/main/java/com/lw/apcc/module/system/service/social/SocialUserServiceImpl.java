package com.lw.apcc.module.system.service.social;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Maps;
import com.lw.apcc.common.enums.CommonStatusEnum;
import com.lw.apcc.common.enums.UserTypeEnum;
import com.lw.apcc.common.exception.ServiceException;
import com.lw.apcc.common.pojo.CommonResult;
import com.lw.apcc.common.pojo.PageResult;
import com.lw.apcc.common.util.collection.CollectionUtils;
import com.lw.apcc.common.util.object.BeanUtils;
import com.lw.apcc.common.util.string.StringUtil;
import com.lw.apcc.common.constants.IdConstants;
import com.lw.apcc.framework.mybatis.core.util.MyBatisUtils;
import com.lw.apcc.framework.web.core.util.WebFrameworkUtils;
import com.lw.apcc.module.coldchain.api.units.UserHasUnitsApi;
import com.lw.apcc.module.system.api.social.dto.SocialUserBindReqDTO;
import com.lw.apcc.module.system.api.social.dto.SocialUserRespDTO;
import com.lw.apcc.module.system.cache.TenantCache;
import com.lw.apcc.module.system.controller.admin.socail.vo.user.SocialUserPageReqVO;
import com.lw.apcc.module.system.controller.admin.socail.vo.user.SocialUserRespVO;
import com.lw.apcc.module.system.controller.admin.socail.vo.user.SocialUserTenantUpdateRepVO;
import com.lw.apcc.module.system.dal.dataobject.permission.role.RoleDO;
import com.lw.apcc.module.system.dal.dataobject.social.SocialUserBindDO;
import com.lw.apcc.module.system.dal.dataobject.social.SocialUserDO;
import com.lw.apcc.module.system.dal.mapper.permission.role.RoleMapper;
import com.lw.apcc.module.system.dal.mapper.social.SocialUserBindMapper;
import com.lw.apcc.module.system.dal.mapper.social.SocialUserMapper;
import com.lw.apcc.module.system.enums.social.SocialTypeEnum;
import com.xingyuv.jushauth.model.AuthUser;
import jakarta.annotation.Resource;
import jakarta.validation.constraints.NotNull;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

import java.time.LocalDateTime;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Set;

import static com.lw.apcc.common.exception.util.ServiceExceptionUtil.exception;
import static com.lw.apcc.common.util.collection.CollectionUtils.convertSet;
import static com.lw.apcc.common.util.json.JsonUtils.toJsonString;
import static com.lw.apcc.module.system.enums.ErrorCodeConstants.SOCIAL_USER_NOT_EXISTS;
import static com.lw.apcc.module.system.enums.ErrorCodeConstants.SOCIAL_USER_NOT_FOUND;

/**
 * 社交用户 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
@Slf4j
public class SocialUserServiceImpl extends ServiceImpl<SocialUserMapper, SocialUserDO> implements SocialUserService {

    @Resource
    private SocialUserBindMapper socialUserBindMapper;
    @Resource
    private SocialUserMapper socialUserMapper;

    @Resource
    private SocialClientService socialClientService;

    @Resource
    private UserHasUnitsApi userUnitsApi;

    @Resource
    private RoleMapper roleMapper;

    @Override
    public List<SocialUserDO> getSocialUserList(Long userId, Integer userType) {
        // 获得绑定
        List<SocialUserBindDO> socialUserBinds = socialUserBindMapper.selectListByUserIdAndUserType(userId, userType);
        if (CollUtil.isEmpty(socialUserBinds)) {
            return Collections.emptyList();
        }
        // 获得社交用户
        return socialUserMapper.selectBatchIds(convertSet(socialUserBinds, SocialUserBindDO::getSocialUserId));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String bindSocialUser(SocialUserBindReqDTO reqDTO) {
        // 获得社交用户
        SocialUserDO socialUser = authSocialUser(reqDTO.getMobile(), reqDTO.getSocialType(), reqDTO.getUserType(), reqDTO.getCode(), reqDTO.getState());
        Assert.notNull(socialUser, "社交用户不能为空");

        // 社交用户可能之前绑定过别的用户，需要进行解绑
        socialUserBindMapper.deleteByUserTypeAndSocialUserId(reqDTO.getUserType(), socialUser.getId());

        // 用户可能之前已经绑定过该社交类型，需要进行解绑
        socialUserBindMapper.deleteByUserTypeAndUserIdAndSocialType(reqDTO.getUserType(), reqDTO.getUserId(),
                socialUser.getType());

        // 绑定当前登录的社交用户
        SocialUserBindDO socialUserBind = SocialUserBindDO.builder()
                // 用户
                .userId(reqDTO.getUserId()).userType(reqDTO.getUserType())
                // 社交用户
                .socialUserId(socialUser.getId()).socialType(socialUser.getType())
                .build();
        socialUserBindMapper.insert(socialUserBind);


        return socialUser.getOpenid();
    }

    @Override
    public void unbindSocialUser(Long userId, Integer userType, Integer socialType, String openid) {
        // 获得 openid 对应的 SocialUserDO 社交用户
        SocialUserDO socialUser = socialUserMapper.selectByTypeAndOpenid(socialType, openid);
        if (socialUser == null) {
            throw exception(SOCIAL_USER_NOT_FOUND);
        }

        // 获得对应的社交绑定关系
        socialUserBindMapper.deleteByUserTypeAndUserIdAndSocialType(userType, userId, socialUser.getType());
    }

    @Override
    public SocialUserRespDTO getSocialUserByUserId(Integer userType, Long userId, Integer socialType) {
        // 获得绑定用户
        SocialUserBindDO socialUserBind = socialUserBindMapper.selectByUserIdAndUserTypeAndSocialType(userId, userType, socialType);
        if (socialUserBind == null) {
            return null;
        }
        // 获得社交用户
        SocialUserDO socialUser = socialUserMapper.selectById(socialUserBind.getSocialUserId());
        Assert.notNull(socialUser, "社交用户不能为空");
        return new SocialUserRespDTO(socialUser.getOpenid(), socialUser.getNickname(), socialUser.getAvatar(),
                socialUserBind.getUserId());
    }

    @Override
    public SocialUserRespDTO getSocialUserByCode(Integer userType, Integer socialType, String code, String state) {
        // 获得社交用户
        SocialUserDO socialUser = authSocialUser(null, socialType, userType, code, state);
        Assert.notNull(socialUser, "社交用户不能为空");

        // 获得绑定用户
        SocialUserBindDO socialUserBind = socialUserBindMapper.selectByUserTypeAndSocialUserId(userType, socialUser.getId());
        return new SocialUserRespDTO(socialUser.getOpenid(), socialUser.getNickname(), socialUser.getAvatar(), socialUserBind != null ? socialUserBind.getUserId() : null);
    }

    /**
     * 授权获得对应的社交用户
     * 如果授权失败，则会抛出 {@link ServiceException} 异常
     *
     * @param socialType 社交平台的类型 {@link SocialTypeEnum}
     * @param userType   用户类型
     * @param code       授权码
     * @param state      state
     * @return 授权用户
     */
    @NotNull
    @Override
    public SocialUserDO authSocialUser(String mobile, Integer socialType, Integer userType, String code, String state) {
        // 优先从 DB 中获取，因为 code 有且可以使用一次。
        // 在社交登录时，当未绑定 User 时，需要绑定登录，此时需要 code 使用两次
        SocialUserDO socialUser = socialUserMapper.selectByTypeAndCodeAnState(socialType, code, state);
        if (socialUser != null) {
            return socialUser;
        }

        // 请求获取
        AuthUser authUser = socialClientService.getAuthUser(socialType, userType, code, state);
        Assert.notNull(authUser, "三方用户不能为空");

        // 保存到 DB 中
        socialUser = socialUserMapper.selectByTypeAndOpenid(socialType, authUser.getUuid());
        if (socialUser == null) {
            socialUser = new SocialUserDO();
        }

        // 取消所有默认租户
        socialUserMapper.updateDefaultByOpenid(socialUser.getOpenid(), CommonStatusEnum.DISABLE.getStatus());

        // 终端Id
        socialUser.setType(socialType)
                // 微信code
                .setCode(code).setState(state) // 需要保存 code + state 字段，保证后续可查询
                // 微信信息
                .setOpenid(authUser.getUuid()).setToken(authUser.getToken().getAccessToken()).setRawTokenInfo((toJsonString(authUser.getToken())))
                // 微信客户端
                .setClientId(WebFrameworkUtils.getClientId())
                // 用户
                .setNickname(authUser.getNickname()).setAvatar(authUser.getAvatar()).setMobile(mobile)
                .setRawUserInfo(toJsonString(authUser.getRawUserInfo()))
                // 默认
                .setIsDefault(CommonStatusEnum.ENABLE.getStatus());
        if (socialUser.getId() == null) {
            socialUserMapper.insert(socialUser);
        } else {
            socialUserMapper.updateById(socialUser);
        }
        return socialUser;
    }

    // ==================== 社交用户 CRUD ====================

    @Override
    public SocialUserDO getSocialUser(Long id) {
        return socialUserMapper.selectById(id);
    }

    @Override
    public PageResult<SocialUserRespVO> pageBySocialUser(SocialUserPageReqVO pageReqVO) {
        if (ObjectUtil.isNotEmpty(pageReqVO.getMobile())) {
            List<String> openidList = socialUserMapper.listOpenidByMobile(pageReqVO.getMobile());
            if (ObjectUtil.isEmpty(openidList)) {
                openidList.add(IdConstants.NON_EXISTENT_ID_STR);
            }
            pageReqVO.setOpenidList(openidList);
        }

        IPage<SocialUserRespVO> iPage = socialUserMapper.pageBySocialUser(MyBatisUtils.buildPage(pageReqVO), pageReqVO);

        if (ObjectUtil.isEmpty(iPage.getRecords())) {
            return MyBatisUtils.buildPageResult(iPage);
        }

        List<SocialUserRespVO> socialUserList = iPage.getRecords();
        Map<Long, Set<String>> unitIdToUnitNames = getUnitIdToUnitNames(CollectionUtils.convertSet(socialUserList, SocialUserRespVO::getUserId));

        iPage.getRecords().forEach(
                respVO -> {
                    respVO.setTenantName(TenantCache.getNameById(respVO.getTenantId()));
                    if (ObjectUtil.isNotEmpty(respVO.getUserId())) {
                        Set<String> unitNames = unitIdToUnitNames.get(respVO.getUserId());
                        respVO.setUnitNames(unitNames);
                    }
                    if (StrUtil.isNotEmpty(respVO.getRoleIds())) {
                        List<String> roleIdList = StringUtil.splitTrim(respVO.getRoleIds(), ',');

                        List<RoleDO> roleList = roleMapper.listByIds(roleIdList);
                        if (ObjectUtil.isNotEmpty(roleList)) {
                            respVO.setRoleNames(CollectionUtils.convertSet(roleList, RoleDO::getName));
                        }
                    }
                }
        );
        return MyBatisUtils.buildPageResult(iPage);
    }

    /**
     * 获得社交用户对应的部门名称
     *
     * @param userIds
     * @return
     */
    private Map<Long, Set<String>> getUnitIdToUnitNames(Set<Long> userIds) {
        if (ObjectUtil.isEmpty(userIds)) {
            return Maps.newHashMap();
        }
        CommonResult<Map<Long, Set<String>>> result = userUnitsApi.getUnitNamesByUserIds(userIds);
        if (result.isSuccess()) {
            return result.getData();
        }
        return Maps.newHashMap();
    }

    /**
     * 设置默认
     *
     * @param id
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public Boolean updateDefaultTenant(Long id) {
        SocialUserDO socialUser = getSocialUser(id);
        if (socialUser == null) {
            throw exception(SOCIAL_USER_NOT_EXISTS);
        }
        // 关闭所有默认
        baseMapper.updateDefaultByOpenid(socialUser.getOpenid(), CommonStatusEnum.DISABLE.getStatus());

        // 设置默认
        baseMapper.updateDefaultById(socialUser.getId(), CommonStatusEnum.ENABLE.getStatus());
        return Boolean.TRUE;
    }

    /**
     * 获取默认
     *
     * @param openid
     * @return
     */
    @Override
    public SocialUserDO getDefaultByOpenid(String openid) {
        return baseMapper.getDefaultByOpenid(openid);
    }

    /**
     * 删除
     *
     * @param id
     * @return
     */
    @Override
    public Boolean removeById(Long id) {
        return baseMapper.removeById(id);
    }

    /**
     * 新增绑定租户
     *
     * @param pageVO
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public Boolean saveByTenant(SocialUserTenantUpdateRepVO pageVO) {
        SocialUserDO socialUser = getSocialUser(pageVO);
        if (socialUser == null) {
            throw exception(SOCIAL_USER_NOT_EXISTS);
        }

        // 关闭所有默认
        baseMapper.updateDefaultByOpenid(socialUser.getOpenid(), CommonStatusEnum.DISABLE.getStatus());

        // 保存或修改
        SocialUserDO tenantSocialUser = baseMapper.getByTenantAndOpenid(pageVO.getTenantId(), socialUser.getOpenid());
        if (tenantSocialUser == null) {
            tenantSocialUser = BeanUtils.toBean(socialUser, SocialUserDO.class);
            tenantSocialUser.setId(null);
            tenantSocialUser.setTenantId(pageVO.getTenantId());
            tenantSocialUser.setCreateTime(LocalDateTime.now());
            tenantSocialUser.setUpdateTime(LocalDateTime.now());
        }
        tenantSocialUser.setIsDefault(CommonStatusEnum.ENABLE.getStatus());
        return super.saveOrUpdate(tenantSocialUser);
    }

    /**
     * 验证社交用户是否已经绑定用户
     *
     * @param socialType
     * @param code
     * @param state
     * @return
     */
    @Override
    public SocialUserBindDO validateRegistration(Integer socialType, String code, String state) {
        Integer value = UserTypeEnum.MEMBER.getValue();
        // 请求获取
        AuthUser authUser = socialClientService.getAuthUser(socialType, value, code, state);
        Assert.notNull(authUser, "三方用户不能为空");

        SocialUserDO socialUser = socialUserMapper.selectByTypeAndOpenid(socialType, authUser.getUuid());
        if (socialUser == null) {
            return null;
        }

        SocialUserBindDO userBind = socialUserBindMapper.selectByUserTypeAndSocialUserId(value, socialUser.getId());
        if (userBind != null) {
            return userBind;
        }
        return null;
    }

    /**
     * 获取社交用户
     *
     * @param pageVO
     * @return
     */
    private SocialUserDO getSocialUser(SocialUserTenantUpdateRepVO pageVO) {
        if (pageVO == null || (pageVO.getOpenid() == null && pageVO.getId() == null)) {
            throw exception(SOCIAL_USER_NOT_EXISTS);
        }
        if (pageVO.getId() == null) {
            return BeanUtils.toBean(pageVO, SocialUserDO.class);
        }
        return getSocialUser(pageVO.getId());
    }

}
