package com.lw.apcc.module.system.service.permission.data;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.lw.apcc.common.pojo.CommonResult;
import com.lw.apcc.common.security.SecurityUtils;
import com.lw.apcc.common.util.collection.CollectionUtils;
import com.lw.apcc.common.constants.IdConstants;
import com.lw.apcc.framework.mybatis.core.service.BaseServiceImpl;
import com.lw.apcc.module.coldchain.api.units.UnitsApi;
import com.lw.apcc.module.coldchain.api.units.UserHasUnitsApi;
import com.lw.apcc.module.coldchain.api.units.dto.UnitTenantIdRespDTO;
import com.lw.apcc.module.system.api.permission.dto.DataPermissionRespDTO;
import com.lw.apcc.module.system.api.permission.dto.TenantDataPermissionRespDTO;
import com.lw.apcc.module.system.cache.DataPermissionCache;
import com.lw.apcc.module.system.cache.TenantDataPermissionCache;
import com.lw.apcc.module.system.controller.admin.permission.vo.data.RoleDataPermissionCustomReqVO;
import com.lw.apcc.module.system.controller.admin.permission.vo.data.RoleDataPermissionCustomRespVO;
import com.lw.apcc.module.system.controller.admin.permission.vo.data.RoleDataPermissionRespVO;
import com.lw.apcc.module.system.controller.admin.permission.vo.data.RoleDataPermissionSaveReqVO;
import com.lw.apcc.module.system.convert.permission.RoleDataPermissionConvert;
import com.lw.apcc.module.system.convert.permission.RoleDataPermissionCustomConvert;
import com.lw.apcc.module.system.dal.dataobject.dept.DeptDO;
import com.lw.apcc.module.system.dal.dataobject.permission.data.DataPermissionDO;
import com.lw.apcc.module.system.dal.dataobject.permission.data.RoleDataPermissionCustomDO;
import com.lw.apcc.module.system.dal.dataobject.permission.data.RoleDataPermissionDO;
import com.lw.apcc.module.system.dal.mapper.area.AreaMapper;
import com.lw.apcc.module.system.dal.mapper.dept.DeptMapper;
import com.lw.apcc.module.system.dal.mapper.permission.data.RoleDataPermissionMapper;
import com.lw.apcc.module.system.dal.mapper.permission.role.UserRoleMapper;
import com.lw.apcc.module.system.dal.mapper.tenant.TenantMapper;
import com.lw.apcc.module.system.dal.mapper.user.UserMapper;
import com.lw.apcc.module.system.enums.permission.DataPermissionCodeEnum;
import com.lw.apcc.module.system.enums.permission.DataPermissionResultTypeEnum;
import com.lw.apcc.module.system.enums.permission.DataPermissionTypeEnum;
import com.lw.apcc.module.system.enums.permission.RoleDataPermissionCustomTypeEnum;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 角色数据权限信息 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class RoleDataPermissionServiceImpl extends BaseServiceImpl<RoleDataPermissionMapper, RoleDataPermissionDO> implements RoleDataPermissionService {

    @Resource
    private RoleDataPermissionCustomService customService;

    @Resource
    private DataPermissionService dataPermissionService;

    @Resource
    private UserRoleMapper userRoleMapper;

    @Resource
    private DeptMapper deptMapper;

    @Resource
    private UserMapper userMapper;

    @Resource
    private AreaMapper areaMapper;

    @Resource
    private TenantMapper tenantMapper;

    @Resource
    private UserHasUnitsApi userHasUnitsApi;

    @Resource
    private UnitsApi unitsApi;

    /**
     * 根据角色id查询角色绑定权限
     *
     * @param roleId
     * @return
     */
    @Override
    public Map<Long, RoleDataPermissionRespVO> getByRoleId(Long roleId) {
        // 数据权限
        List<RoleDataPermissionRespVO> dataPermissionList = baseMapper.listByRoleId(roleId);

        // 自定义数据权限
        Map<Long, List<RoleDataPermissionCustomRespVO>> permissionCustomMap = customService.getDataPermissionCustomByRoleId(roleId);
        dataPermissionList.forEach(dataPermission -> {
            if (dataPermission.getId() != null && DataPermissionTypeEnum.CUSTOM.getType().equals(dataPermission.getPermissionType())) {
                dataPermission.setCustomList(permissionCustomMap.get(dataPermission.getId()));
            }
        });

        return CollectionUtils.convertMap(dataPermissionList, RoleDataPermissionRespVO::getDataPermissionId);
    }

    /**
     * 根据角色id 保存角色数据权限
     *
     * @param roleId
     * @param saveReqList
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public boolean saveRoleDataPermission(Long roleId, List<RoleDataPermissionSaveReqVO> saveReqList) {
        // 删除数据权限
        removeRoleDataPermission(roleId);

        // 转换加载
        List<RoleDataPermissionDO> permissionList = Lists.newArrayList();
        List<RoleDataPermissionCustomDO> permissionCustomList = Lists.newArrayList();

        initRoleDataPermission(permissionList, permissionCustomList, roleId, saveReqList);

        //保存数据权限
        baseMapper.insertBatch(permissionList);
        customService.saveBatch(permissionCustomList);

        DataPermissionCache.clear(SecurityUtils.getTenantId());
        return true;
    }

    /**
     * 获得登陆用户的数据权限
     *
     * @param code
     * @param userId
     * @return
     */
    @Override
    public DataPermissionRespDTO getDataPermissionByCode(String code, Long userId) {
        // ============================================================================
        // 数据权限
        DataPermissionDO dataPermission = dataPermissionService.getByCode(code);
        if (dataPermission == null) {
            return buildNoPermission();
        }
        // 角色
        List<Long> roleIdList = userRoleMapper.listRoleIdByUserId(userId);
        if (ObjectUtil.isEmpty(roleIdList)) {
            return buildNoPermission();
        }

        // 角色绑定数据权限
        List<RoleDataPermissionDO> roleDataPermissionList = getDataPermissionByDataPermissionId(dataPermission.getId(), roleIdList);

        // ============================================================================
        List<String> permissionTypes = Lists.newArrayList();
        // 未获取到权限类型 -- 无权限
        if (ObjectUtil.isEmpty(roleDataPermissionList)) {
            permissionTypes = Lists.newArrayList(dataPermission.getDefaultType());
        }
        // 绑定权限类型
        else {
            permissionTypes = CollectionUtils.convertList(roleDataPermissionList, RoleDataPermissionDO::getPermissionType);
        }

        // 租户数据权限
        TenantDataPermissionRespDTO tenantDataPermission = TenantDataPermissionCache.getDataPermissionByCode(DataPermissionCodeEnum.getByCode(code));
        // 无权限查看
        if (DataPermissionResultTypeEnum.NO.equalsType(tenantDataPermission.getType())) {
            return buildNoPermission();
        }
        // ============================================================================
        // （一级）全部
        if (permissionTypes.contains(DataPermissionTypeEnum.ALL.getType())) {
            return buildByAll(DataPermissionTypeEnum.ALL, tenantDataPermission);
        }

        // 下属区域
        if (permissionTypes.contains(DataPermissionTypeEnum.SUBORDINATE_REGIONS.getType())) {
            return buildBySubordinateRegions(DataPermissionTypeEnum.SUBORDINATE_REGIONS, userId, tenantDataPermission);
        }

        DataPermissionRespDTO permissionRespDTO = null;
        // 自定义设置
        if (permissionTypes.contains(DataPermissionTypeEnum.CUSTOM.getType())) {
            permissionRespDTO = buildByCustom(permissionRespDTO, DataPermissionTypeEnum.CUSTOM, tenantDataPermission, roleDataPermissionList);
        }
        // 仅查看本区域
        if (permissionTypes.contains(DataPermissionTypeEnum.THIS_REGION.getType())) {
            permissionRespDTO = buildByThisRegion(permissionRespDTO, DataPermissionTypeEnum.THIS_REGION, userId, tenantDataPermission);
        }
        // 仅查看本人
        if (permissionTypes.contains(DataPermissionTypeEnum.ONLY_ONESELF.getType())) {
            permissionRespDTO = buildByOnlyOneself(code, permissionRespDTO, DataPermissionTypeEnum.ONLY_ONESELF, userId, tenantDataPermission);
        }
        if (permissionRespDTO != null) {
            return permissionRespDTO;
        }

        // 无权限查看
        return buildNoPermission();
    }

    /**
     * 仅查看本人
     *
     * @param permissionType
     * @param userId
     * @param tenantDataPermission
     * @return
     */
    private DataPermissionRespDTO buildByOnlyOneself(String code, DataPermissionRespDTO permissionRespDTO, DataPermissionTypeEnum permissionType, Long userId, TenantDataPermissionRespDTO tenantDataPermission) {
        if (permissionRespDTO == null) {
            permissionRespDTO = buildPermission(permissionType);
        }

        List<DeptDO> deptList = deptMapper.listByUserId(userId);
        Set<Long> userDeptIds = CollectionUtils.convertSet(deptList, DeptDO::getId);

        // 部门ID
        permissionRespDTO.setDeptIds(convertIds(permissionRespDTO.getDeptIds(), userDeptIds));

        // 用户ID
        permissionRespDTO.setUserIds(convertIds(permissionRespDTO.getUserIds(), Sets.newHashSet(userId)));

        // 区域ID
        Set<Long> areaIds = CollectionUtils.convertSet(deptList, DeptDO::getAreaId);
        permissionRespDTO.setAreaIds(convertIds(permissionRespDTO.getAreaIds(), areaIds));

        // 租户ID
        permissionRespDTO.setTenantIds(convertIds(permissionRespDTO.getTenantIds(), Sets.newHashSet(SecurityUtils.getTenantId())));

        // 《《重要（针对性特殊逻辑）》》 经营主体ID 注：权限类型不是经营主体，直接跳过经营id设置
        if (DataPermissionCodeEnum.UNIT.equalsCode(code)) {
            // 经营主体id只包含自定义，如果经营主体ID集合和别的筛选ID集合同时存在 则使用或的关系筛选经营主体，防止筛除掉新增的经营主体
            CommonResult<Set<Long>> result = userHasUnitsApi.getUnitIdsByUserId(userId);
            if (result.isSuccess()) {
                Set<Long> unitsIds = result.getData();
                permissionRespDTO.setUnitIds(convertIds(permissionRespDTO.getUnitIds(), unitsIds));
                // 加载经营主体租户id权限
                initByOnlyOneselfUnitTenantId(permissionRespDTO, unitsIds);
            }
        }
        return permissionRespDTO;
    }

    /**
     * 加载经营主体租户id权限
     *
     * @param permissionRespDTO
     * @param unitsIds
     */
    private void initByOnlyOneselfUnitTenantId(DataPermissionRespDTO permissionRespDTO, Set<Long> unitsIds) {
        // 获取租户数据权限
        DataPermissionRespDTO dataPermission = DataPermissionCache.getDataPermissionByCode(DataPermissionCodeEnum.TENANT);

        CommonResult<List<UnitTenantIdRespDTO>> result = unitsApi.getTenantIdByIds(unitsIds);
        if (result.isSuccess()) {
            List<UnitTenantIdRespDTO> unitTenantList = CollectionUtils.filter(result.getData(), unitTenant -> dataPermission.getTenantIds().contains(unitTenant.getTenantId()));
            if (ObjectUtil.isNotEmpty(unitTenantList)) {
                permissionRespDTO.setTenantIds(convertIds(permissionRespDTO.getTenantIds(), CollectionUtils.convertSet(unitTenantList, UnitTenantIdRespDTO::getTenantId)));
            }
        }
    }

    /**
     * 自定义设置
     *
     * @param permissionRespDTO
     * @param permissionType
     * @param tenantDataPermission
     * @param roleDataPermissionList
     * @return
     */
    private DataPermissionRespDTO buildByCustom(DataPermissionRespDTO permissionRespDTO, DataPermissionTypeEnum permissionType, TenantDataPermissionRespDTO tenantDataPermission, List<RoleDataPermissionDO> roleDataPermissionList) {
        if (permissionRespDTO == null) {
            permissionRespDTO = buildPermission(permissionType);
        }

        if (ObjectUtil.isEmpty(roleDataPermissionList)) {
            return permissionRespDTO;
        }
        Set<Long> roleDataPermissionIds = CollectionUtils.convertSet(roleDataPermissionList, RoleDataPermissionDO::getId);

        // 部门ID
        Set<Long> deptIds = getCustomDeptIds(roleDataPermissionIds);
        permissionRespDTO.setDeptIds(convertIds(permissionRespDTO.getDeptIds(), deptIds));

        // 用户ID
        Set<Long> userIds = customService.getCustomIdsByCustomType(roleDataPermissionIds, RoleDataPermissionCustomTypeEnum.USER);
        permissionRespDTO.setUserIds(convertIds(permissionRespDTO.getUserIds(), userIds));

        // 区域Path
        Set<String> areaPaths = customService.getCustomPathsByCustomType(roleDataPermissionIds, RoleDataPermissionCustomTypeEnum.AREA, tenantDataPermission);
        permissionRespDTO.setAreaPaths(areaPaths);

        // 租户ID
        Set<Long> tenantIds = getCustomTenantIds(roleDataPermissionIds, areaPaths, tenantDataPermission.getTenantIds());
        permissionRespDTO.setTenantIds(convertIds(permissionRespDTO.getTenantIds(), tenantIds));

        // 经营主体ID
        // 经营主体id只包含自定义，如果经营主体ID集合和别的筛选ID集合同时存在 则使用或的关系筛选经营主体，防止筛除掉新增的经营主体
        Set<Long> unitsIds = getCustomUnitsIds(roleDataPermissionIds, tenantDataPermission);
        permissionRespDTO.setUnitIds(convertIds(permissionRespDTO.getUnitIds(), unitsIds));

        return permissionRespDTO;
    }

    /**
     * 自定义设置经营主体ID
     *
     * @param roleDataPermissionIds
     * @param tenantDataPermission
     * @return
     */
    private Set<Long> getCustomUnitsIds(Set<Long> roleDataPermissionIds, TenantDataPermissionRespDTO tenantDataPermission) {
        Set<Long> unitsIds = customService.getCustomIdsByCustomType(roleDataPermissionIds, RoleDataPermissionCustomTypeEnum.UNITS, tenantDataPermission.getUnitIds());
        CommonResult<List<UnitTenantIdRespDTO>> result = unitsApi.getTenantIdByIds(unitsIds);
        if (result.isSuccess()) {
            List<UnitTenantIdRespDTO> unitTenantList = CollectionUtils.filter(result.getData(), unitTenant -> tenantDataPermission.getTenantIds().contains(unitTenant.getTenantId()));
            if (ObjectUtil.isNotEmpty(unitTenantList)) {
                unitsIds.addAll(CollectionUtils.convertSet(unitTenantList, UnitTenantIdRespDTO::getId));
            }
        }
        return unitsIds;
    }

    /**
     * 自定义部门ID
     *
     * @param roleDataPermissionIds
     * @return
     */
    private Set<Long> getCustomDeptIds(Set<Long> roleDataPermissionIds) {
        Set<Long> deptIds = customService.getCustomIdsByCustomType(roleDataPermissionIds, RoleDataPermissionCustomTypeEnum.DEPT);
        if (ObjectUtil.isEmpty(deptIds)) {
            return Sets.newHashSet();
        }
        return deptMapper.getChildDeptIdsByIds(deptIds);
    }

    private Set<Long> convertIds(Set<Long> ids, Set<Long> addIds) {
        if (ObjectUtil.isEmpty(ids)) {
            ids = Sets.newHashSet();
        }
        ids.addAll(addIds);
        return addIds;
    }

    /**
     * 自定义设置租户ID
     *
     * @param roleDataPermissionIds
     * @param filterAreaIds
     * @return
     */
    private Set<Long> getCustomAreaIds(Set<Long> roleDataPermissionIds, Set<Long> filterAreaIds) {
        Set<Long> areaIds = customService.getCustomIdsByCustomType(roleDataPermissionIds, RoleDataPermissionCustomTypeEnum.AREA);
        areaIds = CollUtil.unionDistinct(filterAreaIds, areaIds);

        Set<Long> childAreaIds = areaMapper.getChildIdsByIds(areaIds, filterAreaIds);
        if (ObjectUtil.isNotEmpty(childAreaIds)) {
            areaIds.addAll(childAreaIds);
        }
        return areaIds;
    }

    /**
     * 自定义设置租户ID
     *
     * @param roleDataPermissionIds
     * @param areaPaths
     * @return
     */
    private Set<Long> getCustomTenantIds(Set<Long> roleDataPermissionIds, Set<String> areaPaths, Set<Long> filterTenantIds) {
        // 查询自定义租户id
        Set<Long> tenantIds = customService.getCustomIdsByCustomType(roleDataPermissionIds, RoleDataPermissionCustomTypeEnum.TENANT);
        // 查询区域对应的租户id
        Set<Long> areaTenantIds = tenantMapper.getIdsByAreaPaths(areaPaths);
        if (ObjectUtil.isNotEmpty(areaTenantIds)) {
            tenantIds.addAll(areaTenantIds);
        }
        // 最大范围为当前租户的范围权限，进行过滤
        tenantIds = CollectionUtils.filterToSet(tenantIds, tenantId -> filterTenantIds.contains(tenantId));
        return tenantIds;
    }

    /**
     * 仅查看本区域
     *
     * @param permissionType
     * @param userId
     * @param tenantDataPermission
     * @return
     */
    private DataPermissionRespDTO buildByThisRegion(DataPermissionRespDTO permissionRespDTO, DataPermissionTypeEnum permissionType, Long userId, TenantDataPermissionRespDTO tenantDataPermission) {
        if (permissionRespDTO == null) {
            permissionRespDTO = buildPermission(permissionType);
        }

        List<DeptDO> deptList = deptMapper.listByUserId(userId);
        Set<Long> userDeptIds = CollectionUtils.convertSet(deptList, DeptDO::getId);

        // 部门ID
        Set<Long> deptIds = deptMapper.getCurrentAreaDeptIdsByIds(userDeptIds);
        permissionRespDTO.setDeptIds(deptIds);

        // 用户ID
        Set<Long> userIds = userMapper.listUserIdsByDeptIds(deptIds);
        permissionRespDTO.setUserIds(userIds);

        // 区域ID
        Set<Long> areaIds = CollectionUtils.convertSet(deptList, DeptDO::getAreaId);
        areaIds = areaMapper.getIdsByDataPermission(areaIds, tenantDataPermission);
        permissionRespDTO.setAreaIds(areaIds);

        // 租户ID
        Set<Long> tenantIds = tenantMapper.getIdsByAreaIds(areaIds);
        Set<Long> tenantIdsAll = CollUtil.unionDistinct(tenantDataPermission.getTenantIds(), tenantIds);
        permissionRespDTO.setTenantIds(tenantIdsAll);

        // 经营主体ID
        permissionRespDTO.setUnitIds(tenantDataPermission.getUnitIds());

        return permissionRespDTO;
    }

    /**
     * 全部
     *
     * @param permissionType
     * @return
     */
    private DataPermissionRespDTO buildByAll(DataPermissionTypeEnum permissionType, TenantDataPermissionRespDTO tenantDataPermission) {
        DataPermissionRespDTO permissionRespDTO = buildPermission(permissionType);
        if (tenantDataPermission != null) {
            permissionRespDTO
                    // 区域ID
                    .setAreaIds(tenantDataPermission.getAreaIds())
                    // 区域PATH
                    .setAreaPath(tenantDataPermission.getAreaPath())
                    // 区域PATH集合
                    .setAreaPaths(tenantDataPermission.getAreaPaths())
                    // 租户ID
                    .setTenantIds(tenantDataPermission.getTenantIds())
                    // 经营主体ID
                    .setUnitIds(tenantDataPermission.getUnitIds());
        }
        return permissionRespDTO;
    }

    /**
     * 下属区域
     *
     * @param permissionType
     * @param userId
     * @return
     */
    private DataPermissionRespDTO buildBySubordinateRegions(DataPermissionTypeEnum permissionType, Long userId, TenantDataPermissionRespDTO tenantDataPermission) {
        DataPermissionRespDTO permissionRespDTO = buildPermission(permissionType);

        List<DeptDO> deptList = deptMapper.listByUserId(userId);
        Set<Long> deptIds = CollectionUtils.convertSet(deptList, DeptDO::getId);
        // 部门ID
        Set<Long> deptIdsAll = deptMapper.getChildAreaDeptIdsByIds(deptIds);
        permissionRespDTO.setDeptIds(deptIdsAll);
        // 用户ID
        Set<Long> userIds = userMapper.listUserIdsByDeptIds(deptIds);
        permissionRespDTO.setUserIds(userIds);

        // 区域
        // 区域PATH
        if (deptList.size() == 1) {
            DeptDO dept = deptList.get(0);
            if (StrUtil.isEmpty(dept.getAreaPath())) {
                permissionRespDTO.setAreaPath(String.valueOf(IdConstants.NON_EXISTENT_ID));
            } else if (
                    dept.getAreaPath().startsWith(StrUtil.emptyIfNull(tenantDataPermission.getAreaPath()))
                            || (ObjectUtil.isNotEmpty(tenantDataPermission.getAreaPaths()) && CollectionUtils.anyMatch(tenantDataPermission.getAreaPaths(), areaPath -> dept.getAreaPath().startsWith(StrUtil.emptyIfNull(areaPath))))
            ) {
                permissionRespDTO.setAreaPath(dept.getAreaPath());
            }
        }
        // 区域ID
        else {
            Set<Long> areaIds = CollectionUtils.convertSet(deptList, DeptDO::getAreaId);
            Set<Long> areaIdsAll = areaMapper.getChildIdsByIds(areaIds, tenantDataPermission.getAreaIds());
            permissionRespDTO.setAreaIds(areaIdsAll);
        }

        permissionRespDTO.setTenantIds(tenantDataPermission.getTenantIds());
        permissionRespDTO.setUnitIds(tenantDataPermission.getUnitIds());
        return permissionRespDTO;
    }

    /**
     * @param permissionType
     * @return
     */
    private DataPermissionRespDTO buildPermission(DataPermissionTypeEnum permissionType) {
        DataPermissionRespDTO respDTO = new DataPermissionRespDTO().setAll(DataPermissionTypeEnum.ALL.equalsType(permissionType.getType())).setPermissionType(permissionType.getType()).setType(permissionType.getResultType().getType())

                .setAreaIds(Sets.newHashSet()).setAreaPath("").setTenantIds(Sets.newHashSet()).setUnitIds(Sets.newHashSet()).setDeptIds(Sets.newHashSet()).setUserIds(Sets.newHashSet());

        return respDTO;
    }

    /**
     * 角色绑定数据权限
     *
     * @param dataPermissionId
     * @param roleIdList
     * @return
     */
    private List<RoleDataPermissionDO> getDataPermissionByDataPermissionId(Long dataPermissionId, List<Long> roleIdList) {
        List<RoleDataPermissionDO> dataPermissionList = baseMapper.listByDataPermissionId(dataPermissionId, roleIdList);
        if (ObjectUtil.isNotEmpty(dataPermissionList)) {
            return dataPermissionList;
        }
        return Lists.newArrayList();
    }

    /**
     * 无权限
     *
     * @return
     */
    private DataPermissionRespDTO buildNoPermission() {
        DataPermissionRespDTO respDTO = new DataPermissionRespDTO().setAll(false).setPermissionType(DataPermissionTypeEnum.NO.getType()).setType(DataPermissionTypeEnum.NO.getResultType().getType())

                .setAreaPath(String.valueOf(IdConstants.NON_EXISTENT_ID)).setAreaPaths(Sets.newHashSet(String.valueOf(IdConstants.NON_EXISTENT_ID))).setAreaIds(Sets.newHashSet(IdConstants.NON_EXISTENT_ID))

                .setTenantIds(Sets.newHashSet(IdConstants.NON_EXISTENT_ID)).setUnitIds(Sets.newHashSet(IdConstants.NON_EXISTENT_ID))

                .setDeptIds(Sets.newHashSet(IdConstants.NON_EXISTENT_ID)).setUserIds(Sets.newHashSet(IdConstants.NON_EXISTENT_ID));
        return respDTO;
    }

    /**
     * 转换加载数据权限
     *
     * @param rolePermissionList
     * @param rolePermissionCustomList
     * @param roleId
     * @param saveReqList
     */
    private void initRoleDataPermission(List<RoleDataPermissionDO> rolePermissionList, List<RoleDataPermissionCustomDO> rolePermissionCustomList, Long roleId, List<RoleDataPermissionSaveReqVO> saveReqList) {
        saveReqList.forEach(saveReq -> {
            // 数据权限
            Long roleDataPermissionId = IdWorker.getId();

            RoleDataPermissionDO dataPermission = RoleDataPermissionConvert.INSTANCE.convert(saveReq);
            dataPermission.setId(roleDataPermissionId);
            dataPermission.setRoleId(roleId);
            rolePermissionList.add(dataPermission);

            // 自定义数据权限
            List<RoleDataPermissionCustomReqVO> permissionCustomList = saveReq.getPermissionCustomList();
            if (ObjectUtil.isNotEmpty(permissionCustomList)) {
                permissionCustomList.forEach(permissionCustom -> {
                    RoleDataPermissionCustomDO dataPermissionCustom = RoleDataPermissionCustomConvert.INSTANCE.convert(permissionCustom);

                    dataPermissionCustom.setRoleId(roleId);
                    dataPermissionCustom.setRoleDataPermissionId(roleDataPermissionId);

                    rolePermissionCustomList.add(dataPermissionCustom);
                });

            }
        });
    }

    /**
     * 删除数据权限
     *
     * @param roleId
     */
    private void removeRoleDataPermission(Long roleId) {
        // 删除
        baseMapper.removeByRoleId(roleId);
        // 删除自定义数据权限
        customService.removeByRoleId(roleId);
    }

}
