package com.lw.apcc.framework.application.enums;

import com.lw.apcc.common.util.collection.CollectionUtils;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.List;

import static com.lw.apcc.framework.application.constants.NacosConstants.NACOS_DEFAULT_PASSWORD;
import static com.lw.apcc.framework.application.constants.NacosConstants.NACOS_DEFAULT_USERNAME;

/**
 * <AUTHOR> yang
 * @description 环境类型枚举
 * @date 2023/12/14 11:31
 */
@Getter
@AllArgsConstructor
public enum EnvironmentEnum {
    /**
     * 开发环境
     */
    DEV("dev", "dev", "开发环境", "************:8848"),
    /**
     * 本地开发环境
     */
    DEV_LOCAL("dev", "dev-local", "本地开发环境", "127.0.0.1:8848"),

    /**
     * 测试环境
     */
    TEST("public", "test", "测试环境", "************:8848"),

    /**
     * 生产环境
     */
    PROD("public", "prod", "生产环境", "mse-52707b50-nacos-ans.mse.aliyuncs.com:8848"),

    /**
     * 本地化环境
     * 千万龘云：***********:8848
     * 原州区：***********:8848
     * 山东信创：nacos.cangchubaoxian.svc.cluster.local:8848
     * 江西：**************:8848
     */
//    LOCAL("public", "prod-local", "生产本地环境", "nacos.cangchubaoxian.svc.cluster.local:8848", "nacos", "Lengwnag888@acc");
//    LOCAL("public", "prod-local", "生产本地环境", "**************:8848", "nacos", "Lengwnag888@acc");
    LOCAL("public", "prod-local", "生产本地环境", "127.0.0.1:8848", "nacos", "Lengwnag888@acc");

    /**
     * 命名空间
     */
    private final String namespace;

    /**
     * 环境编码
     */
    private final String code;

    /**
     * 环境名称
     */
    private final String name;

    /**
     * Naocs 地址
     */
    private final String nacosAddress;

    /**
     * Naocs 用户名
     */
    private final String username;

    /**
     * Naocs 密码
     */
    private final String password;

    /**
     * 构造方法
     *
     * @param namespace
     * @param code
     * @param name
     * @param nacosAddress
     */
    EnvironmentEnum(String namespace, String code, String name, String nacosAddress) {
        this.namespace = namespace;
        this.code = code;
        this.name = name;
        this.nacosAddress = nacosAddress;
        this.username = NACOS_DEFAULT_USERNAME;
        this.password = NACOS_DEFAULT_PASSWORD;
    }

    /**
     * @param code
     * @description 根据 code 获取环境枚举
     * <AUTHOR> yang
     * @date 2023/12/14 11:41
     */
    public static EnvironmentEnum getOneByCode(String code) {
        for (EnvironmentEnum environmentEnum : EnvironmentEnum.values()) {
            if (environmentEnum.getCode().equals(code) || code.endsWith("-" + environmentEnum.getCode())) {
                return environmentEnum;
            }
        }
        //默认开发环境
        return DEV;
    }

    /**
     * @description 获取所有 code
     * <AUTHOR> yang
     * @date 2023/12/14
     */
    public static List<String> listCode() {
        return CollectionUtils.convertList(values(), EnvironmentEnum::getCode);
    }

}
