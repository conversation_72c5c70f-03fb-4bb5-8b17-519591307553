package com.lw.apcc.framework.mybatis.core.handler;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.handlers.MetaObjectHandler;
import com.lw.apcc.common.security.SecurityUtils;
import com.lw.apcc.common.tenant.context.TenantContextHolder;
import com.lw.apcc.common.util.object.ObjectUtil;
import com.lw.apcc.common.constants.IdConstants;
import com.lw.apcc.framework.web.core.util.WebFrameworkUtils;
import org.apache.ibatis.reflection.MetaObject;

import java.time.LocalDateTime;

/**
 * 通用参数填充实现类
 * <p>
 * 如果没有显式的对通用参数进行赋值，这里会对通用参数进行填充、赋值
 *
 * <AUTHOR>
 */
public class DefaultDBFieldHandler implements MetaObjectHandler {

    @Override
    public void insertFill(MetaObject metaObject) {

        LocalDateTime currentDateTime = LocalDateTime.now();

        // 创建时间为空，则以当前时间为插入时间
        if (validateField("createTime", metaObject)) {
            this.setFieldValByName("createTime", currentDateTime, metaObject);
        }
        // 更新时间为空，则以当前时间为更新时间
        if (validateField("updateTime", metaObject)) {
            this.setFieldValByName("updateTime", currentDateTime, metaObject);
        }

        Long userId = SecurityUtils.getLoginUserId();

        // 当前登录用户不为空，创建人为空，则当前登录用户为创建人
        if (validateField("creatorId", metaObject)) {
            this.setFieldValByName("creatorId", userId, metaObject);
        }
        // 当前登录用户不为空，更新人为空，则当前登录用户为更新人
        if (validateField("updaterId", metaObject)) {
            this.setFieldValByName("updaterId", userId, metaObject);
        }

        // 当前租户不为空
        if (validateField("tenantId", metaObject)) {
            Long tenantId = TenantContextHolder.getRequiredTenantId();
            if (tenantId == null) {
                tenantId = ObjectUtil.defaultIfNull(SecurityUtils.getTenantId(), WebFrameworkUtils.getTenantId());
            }
            if (tenantId == null) {
                tenantId = IdConstants.NON_EXISTENT_ID;
            }
            this.setFieldValByName("tenantId", tenantId, metaObject);
        }
    }

    @Override
    public void updateFill(MetaObject metaObject) {
        // 更新时间为空，则以当前时间为更新时间
        if (validateField("updateTime", metaObject)) {
            this.setFieldValByName("updateTime", LocalDateTime.now(), metaObject);
        }

        // 当前登录用户不为空，更新人为空，则当前登录用户为更新人
        if (validateField("updaterId", metaObject)) {
            this.setFieldValByName("updaterId", WebFrameworkUtils.getLoginUserId(), metaObject);
        }
    }

    /**
     * @description 校验字段是否为空
     * <AUTHOR> yang
     * @date 2024/12/20 16:51
     */
    private boolean validateField(String fieldName, MetaObject metaObject) {
        return metaObject.hasGetter(fieldName) && StrUtil.isEmptyIfStr(this.getFieldValByName(fieldName, metaObject));
    }

}
